<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SportLive24 Payment Test - Standalone</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .test-button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 10px 10px 0;
            transition: background 0.3s;
        }

        .test-button:hover {
            background: #e55a2b;
        }

        .success {
            background: #4caf50;
        }

        .success:hover {
            background: #45a049;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .result.success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }

        .result.error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }

        .result.info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1565c0;
        }

        .input-group {
            margin: 10px 0;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🧪 SportLive24 Payment API Test - Standalone</h1>

        <div class="test-section">
            <h3>📋 Test Configuration</h3>
            <div class="input-group">
                <label for="test-email">Email:</label>
                <input type="email" id="test-email" value="<EMAIL>">
            </div>
            <div class="input-group">
                <label for="test-product">Product Slug:</label>
                <input type="text" id="test-product" value="heroes-gate-31">
            </div>
            <div class="input-group">
                <label for="test-name">User Name:</label>
                <input type="text" id="test-name" value="Test User">
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 Basic Tests</h3>
            <button class="test-button" onclick="testHomepage()">Test Homepage</button>
            <button class="test-button" onclick="testCORS()">Test CORS</button>
            <button class="test-button" onclick="testProductAPI()">Test Product API</button>
            <div id="basic-results"></div>
        </div>

        <div class="test-section">
            <h3>💳 Payment Tests</h3>
            <button class="test-button" onclick="testPaymentDirect()">Direct Payment Test</button>
            <button class="test-button" onclick="testPaymentWithCSRF()">Payment with CSRF</button>
            <button class="test-button" onclick="testPaymentNoCSRF()">Payment without CSRF</button>
            <div id="payment-results"></div>
        </div>

        <div class="test-section">
            <h3>🚀 Working Solution</h3>
            <p>Tento test používá přímé volání API bez CSRF problémů:</p>
            <button class="test-button success" onclick="createWorkingPayment()">✅ Create Working Payment</button>
            <div id="working-results"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'https://dev.sportlive24.tv/api';

        function getTestData() {
            return {
                email: document.getElementById('test-email').value,
                product_slug: document.getElementById('test-product').value,
                user_name: document.getElementById('test-name').value
            };
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            container.innerHTML = '';
            container.appendChild(resultDiv);
        }

        async function testHomepage() {
            try {
                const response = await fetch(`${API_BASE.replace('/api', '')}/`);
                const data = await response.json();
                showResult('basic-results', `Homepage OK: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult('basic-results', `Homepage Error: ${error.message}`, 'error');
            }
        }

        async function testCORS() {
            try {
                const response = await fetch(`${API_BASE}/`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                showResult('basic-results', `CORS Test: ${response.status} ${response.statusText}\nHeaders: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`, 'info');
            } catch (error) {
                showResult('basic-results', `CORS Error: ${error.message}`, 'error');
            }
        }

        async function testProductAPI() {
            try {
                const testData = getTestData();
                const response = await fetch(`${API_BASE}/product/${testData.product_slug}/`);
                const data = await response.json();

                if (response.ok) {
                    showResult('basic-results', `Product API OK: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('basic-results', `Product API Error: ${response.status} ${response.statusText}\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('basic-results', `Product API Error: ${error.message}`, 'error');
            }
        }

        async function testPaymentDirect() {
            try {
                const testData = getTestData();
                const response = await fetch(`${API_BASE}/create-payment-from-product/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('payment-results', `Direct Payment OK: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('payment-results', `Direct Payment Error: ${response.status} ${response.statusText}\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('payment-results', `Direct Payment Error: ${error.message}`, 'error');
            }
        }

        async function testPaymentWithCSRF() {
            try {
                // First get CSRF token
                const csrfResponse = await fetch(`${API_BASE.replace('/api', '')}/`);
                const csrfText = await csrfResponse.text();
                const csrfMatch = csrfText.match(/name=['"]csrfmiddlewaretoken['"] value=['"]([^'"]+)['"]/);
                const csrfToken = csrfMatch ? csrfMatch[1] : null;

                const testData = getTestData();
                const response = await fetch(`${API_BASE}/create-payment-from-product/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRFToken': csrfToken || 'no-token'
                    },
                    credentials: 'include',
                    body: JSON.stringify(testData)
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('payment-results', `CSRF Payment OK: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('payment-results', `CSRF Payment Error: ${response.status} ${response.statusText}\nCSRF Token: ${csrfToken || 'not found'}\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('payment-results', `CSRF Payment Error: ${error.message}`, 'error');
            }
        }

        async function testPaymentNoCSRF() {
            try {
                const testData = getTestData();
                const response = await fetch(`${API_BASE}/create-payment-from-product/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    mode: 'cors',
                    body: JSON.stringify(testData)
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('payment-results', `No-CSRF Payment OK: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('payment-results', `No-CSRF Payment Error: ${response.status} ${response.statusText}\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('payment-results', `No-CSRF Payment Error: ${error.message}`, 'error');
            }
        }

        async function createWorkingPayment() {
            try {
                const testData = getTestData();

                // Use curl-like approach - direct API call
                const response = await fetch(`${API_BASE}/create-payment-from-product/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(testData)
                });

                console.log('Working payment response:', response);
                console.log('Working payment headers:', Object.fromEntries(response.headers.entries()));

                const data = await response.json();
                console.log('Working payment data:', data);

                if (response.ok && data.payment_url) {
                    const message = `✅ PAYMENT CREATED SUCCESSFULLY!

Payment ID: ${data.payment_id}
Product: ${data.product_info?.name}
Price: ${data.product_info?.price} ${data.product_info?.currency}
Status: ${data.status}
User exists: ${data.user_exists}
Will create user: ${data.will_create_user}

Payment URL: ${data.payment_url}

🎉 This proves the payment system works correctly!
The issue is only with CSRF protection in the browser.`;

                    showResult('working-results', message, 'success');

                    if (confirm('Payment created successfully! Open payment URL in new tab?')) {
                        window.open(data.payment_url, '_blank');
                    }
                } else {
                    showResult('working-results', `Working Payment Error: ${response.status} ${response.statusText}\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('working-results', `Working Payment Error: ${error.message}`, 'error');
            }
        }

        // Auto-run basic test on load
        window.addEventListener('load', () => {
            console.log('SportLive24 Payment Test loaded');
            console.log('API Base:', API_BASE);
        });
    </script>
</body>
</html>
