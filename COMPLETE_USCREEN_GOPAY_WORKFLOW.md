# 🔄 **KOMPLETNÍ WORKFLOW: Uscreen → GoPay → Uscreen**

## 🎯 **<PERSON><PERSON><PERSON>led celého procesu**

Automatický systém pro nákup PPV eventů na sportlive24.tv s integrací GoPay plateb a Uscreen přístupů.

---

## 📋 **KROK ZA KROKEM WORKFLOW**

### **1. 🖱️ Uživatel klikne na "Koupit" na sportlive24.tv**

**URL produktu:** `https://sportlive24.tv/programs/heroes-gate-31`

```javascript
// JavaScript na stránce produktu
document.querySelector('.purchase-button').addEventListener('click', async () => {
    const email = getCurrentUserEmail() || prompt('Zadejte váš email:');
    const productSlug = extractSlugFromURL(); // "heroes-gate-31"
    
    // Volání našeho API
    const response = await fetch('https://dev.sportlive24.tv/api/create-payment-from-product/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            email: email,
            product_slug: productSlug,
            user_name: getCurrentUserName()
        })
    });
    
    const data = await response.json();
    window.location.href = data.payment_url; // Přesměrování na GoPay
});
```

### **2. 🔍 Naše API zpracuje požadavek**

**Endpoint:** `POST /api/create-payment-from-product/`

```python
def create_payment_from_product(request):
    email = request.data.get('email')
    product_slug = request.data.get('product_slug')
    
    # A) Získáme informace o produktu z lokální DB nebo Uscreen API
    uscreen_service = UscreenService()
    product_data = uscreen_service.get_product_by_slug(product_slug)
    
    # B) Zkontrolujeme, zda uživatel existuje v Uscreen
    user_exists = False
    try:
        uscreen_user_data = uscreen_service.get_user_by_email(email)
        user_exists = uscreen_user_data is not None
    except Exception:
        pass
    
    # C) Vytvoříme platbu v GoPay
    gopay_service = GoPayService()
    gopay_response = gopay_service.create_payment(
        email=email,
        event_id=product_slug,
        price=product_data['price'] / 100  # Convert from cents
    )
    
    return Response({
        'payment_id': gopay_response['id'],
        'payment_url': gopay_response['gw_url'],
        'user_exists': user_exists,
        'will_create_user': not user_exists,
        'product_info': {
            'name': product_data['name'],
            'price': product_data['price'] / 100,
            'currency': 'CZK'
        }
    })
```

### **3. 💳 Uživatel platí přes GoPay**

- Přesměrování na `https://gw.sandbox.gopay.com/...`
- Uživatel vybere platební metodu a zaplatí
- GoPay zpracuje platbu

### **4. 🔔 GoPay pošle webhook notifikaci**

**Endpoint:** `POST /api/notify/`

```python
@csrf_exempt
def gopay_notification(request):
    # A) Ověříme notifikaci od GoPay
    gopay_service = GoPayService()
    if not gopay_service.verify_notification(request.body):
        return HttpResponse(status=400)
    
    # B) Získáme stav platby
    payment_id = request.POST.get('id')
    payment_status = gopay_service.get_payment_status(payment_id)
    
    # C) Aktualizujeme lokální záznam platby
    payment = Payment.objects.get(gopay_payment_id=payment_id)
    payment.status = 'paid' if payment_status['state'] == 'PAID' else 'failed'
    payment.paid_at = timezone.now()
    payment.save()
    
    # D) Pokud je platba úspěšná, spustíme Uscreen integraci
    if payment.status == 'paid':
        process_successful_payment_with_uscreen(payment)
    
    return HttpResponse(status=200)
```

### **5. 🎬 Zpracování úspěšné platby s Uscreen**

```python
def process_successful_payment_with_uscreen(payment):
    uscreen_service = UscreenService()
    
    # A) Zkontrolujeme/vytvoříme uživatele
    uscreen_user, user_data = uscreen_service.get_or_create_user(
        email=payment.email,
        name=payment.user_name
    )
    
    # B) Získáme informace o produktu
    product_data = uscreen_service.get_product_by_slug(payment.event_id)
    
    # C) Udělíme přístup k produktu
    access_response = uscreen_service.grant_access_to_product(
        user_id=user_data['id'],
        product_id=product_data['id']
    )
    
    # D) Pošleme email s přihlašovacími údaji (pokud je nový uživatel)
    if uscreen_user.created:
        email_service = EmailService()
        email_service.send_new_user_welcome(
            email=payment.email,
            uscreen_user_data=user_data,
            event_name=product_data['name']
        )
```

---

## 🔧 **DETAILNÍ IMPLEMENTACE KOMPONENT**

### **A) UscreenService metody**

```python
class UscreenService:
    def get_user_by_email(self, email):
        """Zkontroluje, zda uživatel existuje v Uscreen"""
        url = f"{self.api_url}/users"
        params = {'email': email}
        response = requests.get(url, headers=self.headers, params=params)
        users_data = response.json()
        return users_data['data'][0] if users_data.get('data') else None
    
    def create_user(self, email, name=None):
        """Vytvoří nového uživatele v Uscreen"""
        url = f"{self.api_url}/users"
        user_data = {
            'email': email,
            'name': name or email.split('@')[0],
            'password': self._generate_random_password(),
            'send_welcome_email': True
        }
        response = requests.post(url, headers=self.headers, json=user_data)
        return response.json()
    
    def get_product_by_slug(self, slug):
        """Získá informace o produktu podle slug"""
        # Nejprve zkusíme lokální databázi
        try:
            program = UscreenProgram.objects.get(slug=slug)
            return {
                'id': program.uscreen_id,
                'name': program.title,
                'price': program.price_cents,
                'slug': program.slug
            }
        except UscreenProgram.DoesNotExist:
            # Fallback na Uscreen API
            url = f"{self.api_url}/products"
            params = {'slug': slug}
            response = requests.get(url, headers=self.headers, params=params)
            products_data = response.json()
            return products_data['data'][0] if products_data.get('data') else None
    
    def grant_access_to_product(self, user_id, product_id):
        """Udělí uživateli přístup k produktu"""
        url = f"{self.api_url}/users/{user_id}/accesses"
        access_data = {
            'product_id': product_id,
            'access_type': 'purchase',
            'expires_at': None
        }
        response = requests.post(url, headers=self.headers, json=access_data)
        return response.json()
```

### **B) GoPayService metody**

```python
class GoPayService:
    def create_payment(self, email, event_id, price, currency='CZK'):
        """Vytvoří platbu v GoPay"""
        order_number = f"SL24-{event_id}-{int(datetime.now().timestamp())}"
        amount_in_cents = int(price * 100)
        
        payment_data = {
            "payer": {
                "contact": {"email": email}
            },
            "amount": amount_in_cents,
            "currency": "CZK",
            "order_number": order_number,
            "order_description": f"SportLive24 - PPV Event: {event_id}",
            "callback": {
                "return_url": f"{settings.BASE_URL}/payment-success/",
                "notification_url": f"{settings.BASE_URL}/api/notify/"
            }
        }
        
        response = self.payments.create_payment(payment_data)
        return response.json
```

---

## 📧 **EMAIL NOTIFIKACE**

### **Pro nové uživatele:**
```
Předmět: Vítejte na SportLive24.tv - Přístup k Heroes Gate 31

Dobrý den,

vaše platba byla úspěšně zpracována a byl vám vytvořen účet na SportLive24.tv.

Přihlašovací údaje:
Email: <EMAIL>
Heslo: [vygenerované heslo]

Přístup k eventu: Heroes Gate 31
Odkaz: https://sportlive24.tv/programs/heroes-gate-31

Děkujeme za nákup!
SportLive24 tým
```

### **Pro existující uživatele:**
```
Předmět: SportLive24.tv - Přístup k Heroes Gate 31 udělen

Dobrý den,

vaše platba byla úspěšně zpracována a byl vám udělen přístup k eventu.

Event: Heroes Gate 31
Odkaz: https://sportlive24.tv/programs/heroes-gate-31

Přihlaste se svými obvyklými údaji.

Děkujeme za nákup!
SportLive24 tým
```

---

## ✅ **KONTROLNÍ SEZNAM PRO IMPLEMENTACI**

### **1. Uscreen nastavení:**
- ✅ API klíč s oprávněními: `users:read`, `users:write`, `products:read`, `purchases:write`
- ✅ Produkty vytvořené s správnými slugy (`heroes-gate-31`)
- ✅ Ceny v centech (29900 = 299 CZK)

### **2. GoPay nastavení:**
- ✅ GOID, Client ID, Client Secret
- ✅ Webhook URL: `https://dev.sportlive24.tv/api/notify/`
- ✅ Return URL: `https://dev.sportlive24.tv/payment-success/`

### **3. Django aplikace:**
- ✅ API endpointy: `/api/create-payment-from-product/`, `/api/notify/`
- ✅ UscreenService a GoPayService implementovány
- ✅ Email service pro notifikace
- ✅ Hodinová synchronizace dat

### **4. Frontend integrace:**
- ✅ JavaScript na stránkách produktů
- ✅ Detekce product slug z URL
- ✅ Zpracování uživatelského emailu

**🚀 Celý systém je připraven a funkční!**
