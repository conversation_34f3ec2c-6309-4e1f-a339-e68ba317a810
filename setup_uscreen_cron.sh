#!/bin/bash

# Setup cron jobs for Uscreen data synchronization
# Based on DJANGO-SL24-REPORT implementation

echo "🔧 Setting up Uscreen data synchronization cron jobs..."

# Create cron job file
CRON_FILE="/tmp/uscreen_sync_cron"

cat > $CRON_FILE << 'EOF'
# Uscreen Data Synchronization Cron Jobs
# SportLive24 Payment Integration

# Every hour: Sync events (programs, offers, videos) for last 2 hours
0 * * * * cd /opt/gopay-SL24-payments && /opt/gopay-SL24-payments/venv/bin/python manage.py fetch_events --hours 2 >> /opt/gopay-SL24-payments/logs/uscreen_sync.log 2>&1

# Every 4 hours: Full sync of all data for last 6 hours
0 */4 * * * cd /opt/gopay-SL24-payments && /opt/gopay-SL24-payments/venv/bin/python manage.py sync_all_uscreen_data --hours 6 >> /opt/gopay-SL24-payments/logs/uscreen_sync.log 2>&1

# Daily at 2 AM: Full synchronization of all data
0 2 * * * cd /opt/gopay-SL24-payments && /opt/gopay-SL24-payments/venv/bin/python manage.py sync_all_uscreen_data --full-sync >> /opt/gopay-SL24-payments/logs/uscreen_sync.log 2>&1

# Weekly cleanup: Remove old sync logs (older than 30 days)
0 3 * * 0 cd /opt/gopay-SL24-payments && /opt/gopay-SL24-payments/venv/bin/python manage.py shell -c "from payments.models import UscreenDataSync; from django.utils import timezone; from datetime import timedelta; UscreenDataSync.objects.filter(created_at__lt=timezone.now() - timedelta(days=30)).delete()" >> /opt/gopay-SL24-payments/logs/uscreen_sync.log 2>&1

EOF

# Create logs directory if it doesn't exist
mkdir -p /opt/gopay-SL24-payments/logs

# Install cron jobs
echo "📅 Installing cron jobs..."
crontab -l > /tmp/current_cron 2>/dev/null || true
cat /tmp/current_cron $CRON_FILE | crontab -

# Clean up
rm $CRON_FILE

echo "✅ Cron jobs installed successfully!"
echo ""
echo "📋 Installed cron jobs:"
echo "  • Hourly: Sync events (programs, offers, videos)"
echo "  • Every 4 hours: Full sync of all data"
echo "  • Daily: Complete full synchronization"
echo "  • Weekly: Cleanup old sync logs"
echo ""
echo "📁 Logs location: /opt/gopay-SL24-payments/logs/uscreen_sync.log"
echo ""
echo "🔍 To check cron jobs:"
echo "  crontab -l | grep uscreen"
echo ""
echo "🔍 To monitor logs:"
echo "  tail -f /opt/gopay-SL24-payments/logs/uscreen_sync.log"
