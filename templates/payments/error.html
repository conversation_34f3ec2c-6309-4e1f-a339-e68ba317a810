<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chyba platby - SportLive24.tv</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        .error-icon {
            font-size: 64px;
            color: #e74c3c;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .error-details {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            margin: 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .help-box {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .help-item {
            margin-bottom: 15px;
            padding-left: 25px;
            position: relative;
        }
        .help-item:before {
            content: "💡";
            position: absolute;
            left: 0;
            top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">❌</div>
        <h1>Platba se nezdařila</h1>
        <p class="subtitle">Bohužel došlo k problému při zpracování vaší platby</p>
        
        <div class="error-details">
            <h3 style="margin-top: 0; color: #e74c3c;">Co se stalo?</h3>
            <p>Platba byla zrušena nebo se nepodařila z jednoho z následujících důvodů:</p>
            <ul>
                <li>Platba byla zrušena uživatelem</li>
                <li>Nedostatek prostředků na kartě</li>
                <li>Technický problém s platební bránou</li>
                <li>Vypršel časový limit platby</li>
            </ul>
        </div>

        <div class="help-box">
            <h3 style="margin-top: 0;">💡 Co můžete udělat?</h3>
            
            <div class="help-item">
                <strong>Zkuste platbu znovu</strong><br>
                Většina problémů se vyřeší opakováním platby
            </div>
            
            <div class="help-item">
                <strong>Zkontrolujte platební údaje</strong><br>
                Ověřte číslo karty, datum expirace a CVV kód
            </div>
            
            <div class="help-item">
                <strong>Zkuste jinou platební metodu</strong><br>
                Použijte jinou kartu nebo platební způsob
            </div>
            
            <div class="help-item">
                <strong>Kontaktujte podporu</strong><br>
                Pokud problém přetrvává, napište ná<NAME_EMAIL>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <a href="https://sportlive24.tv/programs/heroes-gate-31" class="btn">
                🔄 Zkusit znovu
            </a>
            <a href="https://sportlive24.tv" class="btn btn-secondary">
                🏠 Zpět na hlavní stránku
            </a>
        </div>

        <div style="margin-top: 20px; font-size: 14px; color: #666;">
            <p>💬 Potřebujete pomoc? Kontaktujte nás:</p>
            <p>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>📞 Telefon: +420 123 456 789</p>
        </div>
    </div>

    <script>
        // Získáme parametry z URL pro diagnostiku
        const urlParams = new URLSearchParams(window.location.search);
        const paymentId = urlParams.get('id');
        const error = urlParams.get('error');
        
        if (paymentId) {
            console.log('Payment ID:', paymentId);
        }
        
        if (error) {
            console.log('Error:', error);
            
            // Můžeme zobrazit specifickou chybu
            const errorDetails = document.querySelector('.error-details ul');
            const errorItem = document.createElement('li');
            errorItem.innerHTML = `<strong>Detaily chyby:</strong> ${error}`;
            errorItem.style.color = '#e74c3c';
            errorDetails.appendChild(errorItem);
        }
    </script>
</body>
</html>
