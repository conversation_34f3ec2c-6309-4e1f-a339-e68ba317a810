<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Platba úspěšná - SportLive24.tv</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success-icon {
            font-size: 64px;
            color: #4CAF50;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .payment-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .detail-label {
            font-weight: 600;
            color: #333;
        }
        .detail-value {
            color: #666;
        }
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            margin: 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #1565c0;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        <h1>Platba byla úspěšná!</h1>
        <p class="subtitle">Děkujeme za váš nákup na SportLive24.tv</p>
        
        <div class="payment-details">
            <div class="detail-row">
                <span class="detail-label">Platba ID:</span>
                <span class="detail-value" id="payment-id">Načítám...</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Produkt:</span>
                <span class="detail-value" id="product-name">Heroes Gate 31</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Částka:</span>
                <span class="detail-value" id="amount">299 CZK</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Stav:</span>
                <span class="detail-value" style="color: #4CAF50; font-weight: 600;">Zaplaceno</span>
            </div>
        </div>

        <div class="info-box">
            <div id="uscreen-status">
                <div class="loading"></div>
                <strong>Zpracovávám přístup k obsahu...</strong>
                <p>Udělujeme vám přístup k zakoupenému obsahu. Tento proces může trvat několik sekund.</p>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <a href="https://sportlive24.tv/programs/heroes-gate-31" class="btn">
                🎬 Přejít na obsah
            </a>
            <a href="https://sportlive24.tv" class="btn btn-secondary">
                🏠 Zpět na hlavní stránku
            </a>
        </div>

        <div style="margin-top: 20px; font-size: 14px; color: #666;">
            <p>📧 Potvrzení platby a přihlašovací údaje vám byly odeslány na email.</p>
            <p>💬 Máte problém? Kontaktujte nás na <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>

    <script>
        // Získáme payment ID z URL
        const urlParams = new URLSearchParams(window.location.search);
        const paymentId = urlParams.get('id');
        
        if (paymentId) {
            document.getElementById('payment-id').textContent = paymentId;
            
            // Zkusíme získat detaily platby
            checkPaymentStatus(paymentId);
        } else {
            document.getElementById('payment-id').textContent = 'Neznámé';
        }

        async function checkPaymentStatus(paymentId) {
            try {
                const response = await fetch(`https://dev.sportlive24.tv/api/payment-status/${paymentId}/`);
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // Aktualizujeme detaily
                    if (data.event_id) {
                        document.getElementById('product-name').textContent = data.event_id;
                    }
                    if (data.price) {
                        document.getElementById('amount').textContent = data.price + ' CZK';
                    }
                    
                    // Zkontrolujeme Uscreen přístup
                    if (data.uscreen_access_granted) {
                        document.getElementById('uscreen-status').innerHTML = `
                            <div style="color: #4CAF50;">
                                <strong>✅ Přístup k obsahu byl úspěšně udělen!</strong>
                                <p>Můžete se nyní přihlásit a sledovat zakoupený obsah.</p>
                            </div>
                        `;
                    } else {
                        // Přístup ještě nebyl udělen, zkusíme to za chvíli znovu
                        setTimeout(() => checkPaymentStatus(paymentId), 5000);
                    }
                } else {
                    console.error('Nepodařilo se načíst stav platby');
                }
            } catch (error) {
                console.error('Chyba při načítání stavu platby:', error);
                document.getElementById('uscreen-status').innerHTML = `
                    <div style="color: #ff9800;">
                        <strong>⚠️ Zpracování přístupu probíhá</strong>
                        <p>Přístup k obsahu bude udělen během několika minut. Zkontrolujte svůj email.</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
