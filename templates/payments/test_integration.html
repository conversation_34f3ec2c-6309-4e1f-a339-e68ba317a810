<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SportLive24 - Heroes Gate 31 - Test Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .product-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .product-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .product-title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 10px;
        }

        .product-subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .product-image {
            width: 100%;
            max-width: 600px;
            height: 300px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5em;
            margin: 0 auto 30px;
        }

        .product-info {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-bottom: 30px;
        }

        .product-details {
            line-height: 1.6;
        }

        .product-details h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .product-details p {
            margin-bottom: 15px;
            color: #555;
        }

        .purchase-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            text-align: center;
        }

        .price {
            font-size: 2em;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 15px;
        }

        .original-price {
            font-size: 1.2em;
            color: #999;
            text-decoration: line-through;
            margin-bottom: 10px;
        }

        .purchase-button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            font-weight: bold;
            border-radius: 6px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 15px;
            transition: background 0.3s;
        }

        .purchase-button:hover {
            background: #e55a2b;
        }

        .purchase-info {
            font-size: 0.9em;
            color: #666;
            line-height: 1.4;
        }

        .test-controls {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }

        .test-controls h3 {
            margin-top: 0;
            color: #1976d2;
        }

        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .test-button:hover {
            background: #1976d2;
        }

        .user-simulation {
            margin-top: 15px;
        }

        .user-simulation input {
            padding: 8px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .product-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <h3>🧪 Test Controls - SportLive24 Payment Integration</h3>
        <p>Tato stránka simuluje stránku produktu na sportlive24.tv pro testování platební integrace.</p>

        <div>
            <button class="test-button" onclick="testApiStatus()">Test API Status</button>
            <button class="test-button" onclick="testMockProductInfo()">Test Mock Product API</button>
            <button class="test-button" onclick="testProductInfo()">Test Product Info API</button>
            <button class="test-button" onclick="testPaymentCreation()">Test Payment Creation</button>
            <button class="test-button" onclick="testMockPaymentCreation()">Test Mock Payment</button>
            <button class="test-button" onclick="simulateLoggedUser()">Simulate Logged User</button>
            <button class="test-button" onclick="clearUserSimulation()">Clear User</button>
            <button class="test-button" onclick="debugConnectivity()">Debug Connectivity</button>
            <button class="test-button" onclick="testQuickPayment()">🚀 Quick Test Payment</button>
            <button class="test-button" onclick="testQuickStatus()">🔍 Quick Test Status</button>
        </div>

        <div class="user-simulation">
            <strong>Simulate User:</strong>
            <input type="email" id="sim-email" placeholder="Email" value="<EMAIL>">
            <input type="text" id="sim-name" placeholder="Name" value="Test User">
            <button class="test-button" onclick="setSimulatedUser()">Set User</button>
        </div>
    </div>

    <div class="product-container">
        <div class="product-header">
            <h1 class="product-title">Heroes Gate 31</h1>
            <p class="product-subtitle">Exkluzivní PPV Event - MMA Championship</p>
        </div>

        <div class="product-image">
            🥊 HEROES GATE 31 🥊
        </div>

        <div class="product-info">
            <div class="product-details">
                <h3>O eventu</h3>
                <p>
                    Připravte se na nezapomenutelný večer plný adrenalinu! Heroes Gate 31 přináší
                    nejlepší bojovníky z celého světa v epických soubojích, které rozhodnou o novém šampionovi.
                </p>

                <h3>Program</h3>
                <p>
                    <strong>Datum:</strong> 15. prosince 2024<br>
                    <strong>Čas:</strong> 20:00 CET<br>
                    <strong>Místo:</strong> O2 Arena, Praha
                </p>

                <h3>Hlavní zápasy</h3>
                <ul>
                    <li>Šampionský zápas - Heavyweight Division</li>
                    <li>Co-main event - Light Heavyweight</li>
                    <li>Speciální exhibition match</li>
                    <li>+ další 6 zápasů</li>
                </ul>

                <h3>Co získáte</h3>
                <ul>
                    <li>✅ Přístup k živému přenosu v HD kvalitě</li>
                    <li>✅ Možnost sledování až 7 dní po eventu</li>
                    <li>✅ Exkluzivní zákulisní obsah</li>
                    <li>✅ Komentáře v češtině i angličtině</li>
                </ul>
            </div>

            <div class="purchase-panel">
                <div class="original-price">Původní cena: 399 CZK</div>
                <div class="price">299 CZK</div>

                <button class="purchase-button" data-action="purchase">
                    🛒 Koupit PPV přístup
                </button>

                <div class="purchase-info">
                    ℹ️ Po platbě bude automaticky vytvořen účet na SportLive24 platformě s přístupovými údaji zaslanými emailem.
                    <br><br>
                    💳 Platba přes GoPay - bezpečně a rychle
                </div>
            </div>
        </div>
    </div>

    <!-- SportLive24 Payment Integration Script -->
    <script src="/static/js/sportlive24-payment-integration.js"></script>

    <!-- Test Functions -->
    <script>
        // Test functions for development
        async function testProductInfo() {
            try {
                console.log('Testing product info API...');
                const response = await fetch('https://dev.sportlive24.tv/api/product/heroes-gate-31/');
                console.log('Response status:', response.status);

                const data = await response.json();
                console.log('Product Info:', data);

                if (response.ok) {
                    alert(`Product Info loaded successfully!\nName: ${data.name}\nPrice: ${data.price} ${data.currency}\nSource: ${data.source || 'unknown'}`);
                } else {
                    alert(`API Error: ${data.error || 'Unknown error'}`);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error loading product info: ' + error.message);
            }
        }

        async function testMockProductInfo() {
            try {
                console.log('Testing mock product info API...');
                const response = await fetch('https://dev.sportlive24.tv/api/mock-product/heroes-gate-31/');
                console.log('Response status:', response.status);

                const data = await response.json();
                console.log('Mock Product Info:', data);

                if (response.ok) {
                    alert(`Mock Product Info loaded successfully!\nName: ${data.name}\nPrice: ${data.price} ${data.currency}\nSource: ${data.source || 'unknown'}`);
                } else {
                    alert(`API Error: ${data.error || 'Unknown error'}`);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error loading mock product info: ' + error.message);
            }
        }

        async function testApiStatus() {
            try {
                console.log('Testing API status...');
                const response = await fetch('https://dev.sportlive24.tv/api/test-api-status/');
                const data = await response.json();
                console.log('API Status:', data);
                alert('API Status: ' + data.status + '\nMessage: ' + data.message);
            } catch (error) {
                console.error('Error:', error);
                alert('Error testing API status: ' + error.message);
            }
        }

        async function testPaymentCreation() {
            const email = prompt('Enter email for test payment:') || '<EMAIL>';

            try {
                console.log('Creating test payment for:', email);
                const response = await fetch('https://dev.sportlive24.tv/api/create-payment-from-product/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        product_slug: 'heroes-gate-31',
                        user_name: 'Test User'
                    })
                });

                console.log('Payment response status:', response.status);
                console.log('Payment response headers:', Object.fromEntries(response.headers.entries()));

                let data;
                try {
                    data = await response.json();
                    console.log('Payment Response:', data);
                } catch (parseError) {
                    console.error('Failed to parse JSON response:', parseError);
                    const textResponse = await response.text();
                    console.log('Raw response:', textResponse);
                    alert(`Payment creation failed!\nHTTP ${response.status}: ${response.statusText}\nResponse: ${textResponse.substring(0, 200)}...`);
                    return;
                }

                if (response.ok && data.payment_url) {
                    alert(`Payment created successfully!\nPayment ID: ${data.payment_id}\nProduct: ${data.product_info?.name}\nPrice: ${data.product_info?.price} ${data.product_info?.currency}\n\nOpen payment URL?`);
                    if (confirm('Open payment URL in new tab?')) {
                        window.open(data.payment_url, '_blank');
                    }
                } else {
                    const errorMsg = data.error || `HTTP ${response.status}: ${response.statusText}`;
                    alert(`Payment creation failed!\nError: ${errorMsg}`);
                    console.error('Payment creation failed:', data);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error creating payment: ' + error.message);
            }
        }

        async function testMockPaymentCreation() {
            const email = prompt('Enter email for mock test payment:') || '<EMAIL>';

            try {
                console.log('Creating mock test payment for:', email);
                const response = await fetch('https://dev.sportlive24.tv/api/mock-create-payment/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: email,
                        product_slug: 'heroes-gate-31',
                        user_name: 'Test User'
                    })
                });

                console.log('Mock payment response status:', response.status);

                const data = await response.json();
                console.log('Mock Payment Response:', data);

                if (response.ok && data.payment_url) {
                    alert(`Mock Payment created successfully!\nPayment ID: ${data.payment_id}\nProduct: ${data.product_info?.name}\nPrice: ${data.product_info?.price} ${data.product_info?.currency}\nSource: ${data.source}\n\nThis is a mock payment for testing.`);
                } else {
                    const errorMsg = data.error || `HTTP ${response.status}: ${response.statusText}`;
                    alert(`Mock payment creation failed!\nError: ${errorMsg}`);
                    console.error('Mock payment creation failed:', data);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error creating mock payment: ' + error.message);
            }
        }

        function simulateLoggedUser() {
            // Simulace přihlášeného uživatele v Uscreen
            window.Uscreen = {
                user: {
                    email: '<EMAIL>',
                    name: 'Logged User',
                    first_name: 'Logged',
                    last_name: 'User'
                }
            };

            // Reinicializace payment systému
            window.SportLive24Payment.detectCurrentUser();
            alert('Simulated logged user: <EMAIL>');
        }

        function clearUserSimulation() {
            delete window.Uscreen;
            window.SportLive24Payment.currentUser = null;
            alert('User simulation cleared');
        }

        function setSimulatedUser() {
            const email = document.getElementById('sim-email').value;
            const name = document.getElementById('sim-name').value;

            if (!email) {
                alert('Please enter email');
                return;
            }

            window.Uscreen = {
                user: {
                    email: email,
                    name: name,
                    first_name: name.split(' ')[0],
                    last_name: name.split(' ')[1] || ''
                }
            };

            window.SportLive24Payment.detectCurrentUser();
            alert(`Simulated user set: ${email}`);
        }

        async function testQuickPayment() {
            const email = prompt('Enter email for quick test payment:') || '<EMAIL>';

            try {
                console.log('Testing quick payment endpoint...');
                const response = await fetch('https://dev.sportlive24.tv/api/quick-test-payment/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: email,
                        product_slug: 'heroes-gate-31',
                        user_name: 'Quick Test User'
                    })
                });

                console.log('Quick payment response status:', response.status);

                const data = await response.json();
                console.log('Quick Payment Response:', data);

                if (response.ok) {
                    alert(`🚀 Quick Payment Test SUCCESS!\n\nPayment ID: ${data.payment_id}\nProduct: ${data.product_info?.name}\nPrice: ${data.product_info?.price} ${data.product_info?.currency}\nSource: ${data.source}\nMessage: ${data.message}\n\nThis proves the payment system works!`);
                } else {
                    alert(`❌ Quick payment test failed!\nError: ${data.error || 'Unknown error'}`);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('❌ Quick payment test error: ' + error.message);
            }
        }

        async function testQuickStatus() {
            try {
                console.log('Testing quick status endpoint...');
                const response = await fetch('https://dev.sportlive24.tv/api/quick-test-status/');

                console.log('Quick status response status:', response.status);

                const data = await response.json();
                console.log('Quick Status Response:', data);

                if (response.ok) {
                    alert(`🔍 Quick Status Test SUCCESS!\n\nStatus: ${data.status}\nMessage: ${data.message}\nTimestamp: ${data.timestamp}\n\nAvailable endpoints:\n${Object.keys(data.endpoints).join(', ')}`);
                } else {
                    alert(`❌ Quick status test failed!\nError: ${data.error || 'Unknown error'}`);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('❌ Quick status test error: ' + error.message);
            }
        }

        async function debugConnectivity() {
            console.log('=== DEBUG CONNECTIVITY ===');
            console.log('Current URL:', window.location.href);
            console.log('User Agent:', navigator.userAgent);
            console.log('Cookies:', document.cookie);

            // Test basic connectivity
            try {
                const response = await fetch('https://dev.sportlive24.tv/', {
                    method: 'GET',
                    credentials: 'include'
                });
                console.log('Homepage response status:', response.status);
                console.log('Homepage response headers:', Object.fromEntries(response.headers.entries()));

                // Test CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
                console.log('CSRF token found:', csrfToken ? csrfToken.value : 'None');

                // Test POST with different headers
                const testResponse = await fetch('https://dev.sportlive24.tv/api/create-payment-from-product/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        product_slug: 'heroes-gate-31'
                    })
                });

                console.log('Test POST response status:', testResponse.status);
                console.log('Test POST response headers:', Object.fromEntries(testResponse.headers.entries()));

                if (testResponse.ok) {
                    const testData = await testResponse.json();
                    console.log('Test POST response data:', testData);
                    alert('✅ Connectivity test PASSED!\nPayment API is working correctly.\nCheck console for details.');
                } else {
                    const errorText = await testResponse.text();
                    console.log('Test POST error response:', errorText);
                    alert(`❌ Connectivity test FAILED!\nHTTP ${testResponse.status}: ${testResponse.statusText}\nCheck console for details.`);
                }

            } catch (error) {
                console.error('Connectivity test error:', error);
                alert('❌ Connectivity test ERROR!\n' + error.message + '\nCheck console for details.');
            }
        }

        // Log current URL for debugging
        console.log('Current URL:', window.location.href);
        console.log('Extracted product slug:', window.SportLive24Payment.extractProductSlugFromUrl());
    </script>
</body>
</html>
