# 📊 Uscreen Data Synchronization Guide

## 🎯 **Přehled**

Systém pro synchronizaci dat z Uscreen API do naší platební aplikace. Inspirováno implementací z `/opt/DJANGO-SL24-REPORT`.

---

## 🗄️ **Nové modely**

### **UscreenProgram** - PPV programy/eventy
```python
- uscreen_id: Unique ID z Uscreen
- title: Název programu
- slug: URL slug
- price_cents: Cena v centech
- event_state: Stav eventu
- author_name: <PERSON><PERSON><PERSON> autora
- main_poster_url: URL hlavního posteru
- tags: J<PERSON><PERSON> pole tagů
```

### **UscreenOffer** - <PERSON><PERSON><PERSON><PERSON><PERSON>
```python
- uscreen_id: Unique ID z Uscreen
- title: Název nabídky
- offer_type: Typ nabídky
- price_cents: Cena v centech
- private: Zda je privátní
```

### **UscreenVideo** - Videa
```python
- uscreen_id: Unique ID z Uscreen
- title: Název videa
- filename: Název souboru
- duration: Délka v sekundách
- size: Velikost v bytech
```

### **UscreenUser** - Uživatelé
```python
- uscreen_id: Unique ID z Uscreen
- email: Email uživatele
- name: Jméno uživatele
- subscriber: Zda je předplatitel
- custom_fields: JSON vlastní pole
```

### **UscreenInvoice** - Faktury
```python
- uscreen_id: Unique ID z Uscreen
- status: Stav faktury
- amount: Částka v centech
- user_id: ID uživatele
- product_id: ID produktu
- paid_at: Datum zaplacení
```

### **UscreenDataSync** - Log synchronizace
```python
- sync_type: Typ synchronizace
- last_sync: Čas poslední synchronizace
- success: Zda byla úspěšná
- records_processed: Počet zpracovaných záznamů
- error_message: Chybová zpráva
```

---

## 🔧 **Management Commands**

### **1. Hlavní synchronizace**
```bash
# Synchronizace všech dat za posledních 24 hodin
python manage.py fetch_uscreen_data

# Synchronizace konkrétního typu dat
python manage.py fetch_uscreen_data --type programs
python manage.py fetch_uscreen_data --type offers
python manage.py fetch_uscreen_data --type videos
python manage.py fetch_uscreen_data --type users
python manage.py fetch_uscreen_data --type invoices

# Úplná synchronizace (všechna data)
python manage.py fetch_uscreen_data --full-sync

# Synchronizace za posledních 48 hodin
python manage.py fetch_uscreen_data --hours 48
```

### **2. Synchronizace pouze eventů**
```bash
# Synchronizace programs, offers a videos
python manage.py fetch_events

# S úplnou synchronizací
python manage.py fetch_events --full-sync
```

### **3. Úplná synchronizace všech dat**
```bash
# Kompletní synchronizace s přehledem
python manage.py sync_all_uscreen_data

# Přeskočit faktury (rychlejší)
python manage.py sync_all_uscreen_data --skip-invoices

# Úplná synchronizace všech dat
python manage.py sync_all_uscreen_data --full-sync
```

### **4. Test připojení**
```bash
# Test připojení k API
python manage.py test_uscreen_fetch

# Test konkrétního typu dat
python manage.py test_uscreen_fetch --type programs
```

---

## ⚙️ **Nastavení**

### **1. API klíč**
```bash
# V .env souboru
USCREEN_API_KEY=sk_live_your_api_key_here
```

### **2. Cron job pro automatickou synchronizaci**
```bash
# Každou hodinu synchronizace eventů
0 * * * * cd /opt/gopay-SL24-payments && python manage.py fetch_events --hours 2

# Každé 4 hodiny úplná synchronizace
0 */4 * * * cd /opt/gopay-SL24-payments && python manage.py sync_all_uscreen_data --hours 6

# Denní úplná synchronizace
0 2 * * * cd /opt/gopay-SL24-payments && python manage.py sync_all_uscreen_data --full-sync
```

---

## 📋 **Admin rozhraní**

### **Dostupné sekce:**
- **Uscreen Programs** - Správa PPV programů
- **Uscreen Offers** - Správa nabídek
- **Uscreen Videos** - Správa videí
- **Uscreen Users** - Správa uživatelů
- **Uscreen Invoices** - Správa faktur
- **Uscreen Data Sync** - Log synchronizací

### **URL:**
```
https://dev.sportlive24.tv/admin/payments/
```

---

## 🔍 **Monitoring a ladění**

### **1. Kontrola logů synchronizace**
```python
from payments.models import UscreenDataSync

# Poslední synchronizace
latest_syncs = UscreenDataSync.objects.order_by('-last_sync')[:10]
for sync in latest_syncs:
    print(f"{sync.sync_type}: {sync.success} - {sync.records_processed} records")
```

### **2. Statistiky dat**
```python
from payments.models import UscreenProgram, UscreenOffer, UscreenVideo

print(f"Programs: {UscreenProgram.objects.count()}")
print(f"Offers: {UscreenOffer.objects.count()}")
print(f"Videos: {UscreenVideo.objects.count()}")
```

### **3. Kontrola chyb**
```python
# Neúspěšné synchronizace
failed_syncs = UscreenDataSync.objects.filter(success=False)
for sync in failed_syncs:
    print(f"FAILED {sync.sync_type}: {sync.error_message}")
```

---

## 🚀 **Použití v platebním systému**

### **1. Aktualizace UscreenService**
Nyní můžeme používat lokální data místo API volání:

```python
# Místo API volání
product_data = uscreen_service.get_product_by_slug(slug)

# Použijeme lokální data
try:
    program = UscreenProgram.objects.get(slug=slug)
    product_data = {
        'id': program.uscreen_id,
        'name': program.title,
        'price': program.price_cents,
        'slug': program.slug
    }
except UscreenProgram.DoesNotExist:
    # Fallback na API
    product_data = uscreen_service.get_product_by_slug(slug)
```

### **2. Párování plateb s produkty**
```python
# V Payment modelu můžeme nyní linkovat na konkrétní produkty
payment.uscreen_program = UscreenProgram.objects.get(slug=event_slug)
payment.uscreen_offer = UscreenOffer.objects.get(uscreen_id=offer_id)
payment.save()
```

---

## 📈 **Výhody nového systému**

✅ **Rychlejší odezva** - Data jsou lokální, ne API volání  
✅ **Offline funkčnost** - Funguje i při výpadku Uscreen API  
✅ **Lepší monitoring** - Vidíme historii synchronizací  
✅ **Flexibilní dotazy** - Můžeme filtrovat a vyhledávat  
✅ **Automatické aktualizace** - Cron job udržuje data aktuální  
✅ **Konzistentní s REPORT** - Stejný pattern jako v DJANGO-SL24-REPORT  

---

## 🔧 **Další kroky**

1. **Nastavit cron job** pro automatickou synchronizaci
2. **Aktualizovat UscreenService** pro použití lokálních dat
3. **Přidat monitoring** synchronizací
4. **Optimalizovat** dotazy pro lepší výkon
5. **Přidat webhooks** pro real-time aktualizace
