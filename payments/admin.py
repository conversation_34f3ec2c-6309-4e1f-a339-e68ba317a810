from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Payment, PaymentNotification, UscreenUser, UscreenProgram,
    UscreenOffer, UscreenVideo, UscreenInvoice, UscreenDataSync
)


@admin.register(UscreenUser)
class UscreenUserAdmin(admin.ModelAdmin):
    list_display = ('uscreen_id', 'name', 'email', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name', 'email', 'uscreen_id')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(UscreenProgram)
class UscreenProgramAdmin(admin.ModelAdmin):
    list_display = ('uscreen_id', 'title', 'slug', 'price_czk', 'event_state', 'uscreen_created_at')
    list_filter = ('event_state', 'part_of_subscription', 'content_type', 'uscreen_created_at')
    search_fields = ('title', 'slug', 'uscreen_id', 'author_name')
    readonly_fields = ('uscreen_id', 'uscreen_created_at', 'created_at', 'updated_at')
    ordering = ('-uscreen_created_at',)

    fieldsets = (
        ('Basic Information', {
            'fields': ('uscreen_id', 'title', 'description', 'slug')
        }),
        ('Program Details', {
            'fields': ('part_of_subscription', 'price_cents', 'rental_price_cents',
                      'rental_period', 'content_type', 'event_state')
        }),
        ('Media', {
            'fields': ('main_poster_url', 'secondary_poster_url')
        }),
        ('Author', {
            'fields': ('author_name', 'author_description', 'author_image')
        }),
        ('Metadata', {
            'fields': ('tags', 'subscription_plans', 'event_launch_date')
        }),
        ('Timestamps', {
            'fields': ('uscreen_created_at', 'created_at', 'updated_at')
        }),
    )


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('gopay_payment_id', 'email', 'event_id', 'status', 'price', 'currency', 'created_at', 'uscreen_access_granted')
    list_filter = ('status', 'currency', 'uscreen_access_granted', 'created_at')
    search_fields = ('email', 'event_id', 'gopay_payment_id', 'gopay_order_number')
    readonly_fields = ('created_at', 'updated_at', 'paid_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('email', 'event_id', 'price', 'currency', 'status')
        }),
        ('GoPay Details', {
            'fields': ('gopay_payment_id', 'gopay_order_number', 'gopay_state', 'gopay_sub_state', 'gopay_payment_instrument')
        }),
        ('Uscreen Integration', {
            'fields': ('uscreen_user', 'uscreen_program', 'uscreen_access_granted')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'paid_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('uscreen_user', 'uscreen_program')


@admin.register(PaymentNotification)
class PaymentNotificationAdmin(admin.ModelAdmin):
    list_display = ('payment', 'gopay_payment_id', 'processed', 'created_at')
    list_filter = ('processed', 'created_at')
    search_fields = ('gopay_payment_id', 'payment__email')
    readonly_fields = ('created_at', 'notification_data')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('payment')


@admin.register(UscreenOffer)
class UscreenOfferAdmin(admin.ModelAdmin):
    list_display = ('uscreen_id', 'title', 'offer_type', 'price_czk', 'private', 'uscreen_created_at')
    list_filter = ('offer_type', 'private', 'uscreen_created_at')
    search_fields = ('title', 'uscreen_id')
    readonly_fields = ('uscreen_id', 'uscreen_created_at', 'created_at', 'updated_at')
    ordering = ('-uscreen_created_at',)


@admin.register(UscreenVideo)
class UscreenVideoAdmin(admin.ModelAdmin):
    list_display = ('uscreen_id', 'title', 'filename', 'duration_formatted', 'size_formatted', 'uscreen_created_at')
    list_filter = ('source', 'uscreen_created_at')
    search_fields = ('title', 'filename', 'uscreen_id')
    readonly_fields = ('uscreen_id', 'uscreen_created_at', 'created_at', 'updated_at')
    ordering = ('-uscreen_created_at',)

    def duration_formatted(self, obj):
        if obj.duration:
            minutes, seconds = divmod(obj.duration, 60)
            hours, minutes = divmod(minutes, 60)
            if hours:
                return f"{hours}h {minutes}m {seconds}s"
            return f"{minutes}m {seconds}s"
        return "-"
    duration_formatted.short_description = "Duration"

    def size_formatted(self, obj):
        if obj.size:
            # Convert bytes to MB
            size_mb = obj.size / (1024 * 1024)
            if size_mb > 1024:
                size_gb = size_mb / 1024
                return f"{size_gb:.1f} GB"
            return f"{size_mb:.1f} MB"
        return "-"
    size_formatted.short_description = "Size"


@admin.register(UscreenInvoice)
class UscreenInvoiceAdmin(admin.ModelAdmin):
    list_display = ('uscreen_id', 'status', 'amount_czk', 'currency', 'user_id', 'product_id', 'paid_at')
    list_filter = ('status', 'currency', 'product_type', 'trial', 'paid_at')
    search_fields = ('uscreen_id', 'user_id', 'product_id')
    readonly_fields = ('uscreen_id', 'uscreen_created_at', 'uscreen_updated_at', 'created_at', 'updated_at')
    ordering = ('-paid_at', '-uscreen_created_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('uscreen_id', 'status', 'amount', 'currency')
        }),
        ('Product & User', {
            'fields': ('user_id', 'product_id', 'product_type')
        }),
        ('Payment Details', {
            'fields': ('discount', 'trial', 'gift_amount', 'origin', 'coupon')
        }),
        ('Localized Amounts', {
            'fields': ('localized_currency', 'localized_amount', 'localized_discount', 'localized_gift_amount')
        }),
        ('Timestamps', {
            'fields': ('paid_at', 'uscreen_created_at', 'uscreen_updated_at', 'created_at', 'updated_at')
        }),
    )


@admin.register(UscreenDataSync)
class UscreenDataSyncAdmin(admin.ModelAdmin):
    list_display = ('sync_type', 'last_sync', 'success', 'records_processed', 'error_message_short')
    list_filter = ('sync_type', 'success', 'last_sync')
    search_fields = ('sync_type', 'error_message')
    readonly_fields = ('created_at',)
    ordering = ('-last_sync',)

    def error_message_short(self, obj):
        if obj.error_message:
            return obj.error_message[:100] + "..." if len(obj.error_message) > 100 else obj.error_message
        return "-"
    error_message_short.short_description = "Error Message"
