from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from decimal import Decimal
import json
import logging

from .models import Payment, PaymentNotification, UscreenUser, UscreenProgram
from .services.gopay_service import GoPayService
from .services.uscreen_service import UscreenService

logger = logging.getLogger(__name__)


@csrf_exempt
@api_view(['POST'])
def create_payment(request):
    """
    Create a new payment via GoPay

    POST /api/create-payment/
    {
        "email": "<EMAIL>",
        "event_id": "event-slug-123",
        "price": 299.00
    }
    """
    try:
        data = request.data

        # Validate required fields
        email = data.get('email')
        event_id = data.get('event_id')
        price = data.get('price')

        if not all([email, event_id, price]):
            return Response({
                'error': 'Missing required fields: email, event_id, price'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Convert price to Decimal
        try:
            price = Decimal(str(price))
        except (ValueError, TypeError):
            return Response({
                'error': 'Invalid price format'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Initialize GoPay service
        gopay_service = GoPayService()

        # Create payment in GoPay
        gopay_response = gopay_service.create_payment(
            email=email,
            event_id=event_id,
            price=price
        )

        # Create local payment record
        payment = Payment.objects.create(
            email=email,
            event_id=event_id,
            gopay_payment_id=gopay_response['id'],
            price=price,
            gopay_order_number=gopay_response.get('order_number'),
            gopay_state=gopay_response.get('state'),
            gopay_sub_state=gopay_response.get('sub_state')
        )

        logger.info(f"Created payment {payment.id} for {email}")

        # Return payment URL for redirect
        return Response({
            'payment_id': payment.gopay_payment_id,
            'payment_url': gopay_response['gw_url'],
            'status': 'created'
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error creating payment: {e}")
        return Response({
            'error': f'Payment creation failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@require_http_methods(["POST"])
def gopay_notification(request):
    """
    Handle GoPay webhook notifications

    POST /api/notify/
    """
    try:
        # Get notification data
        notification_data = json.loads(request.body.decode('utf-8'))

        # Get signature from headers (if provided by GoPay)
        signature = request.META.get('HTTP_X_SIGNATURE', '')

        logger.info(f"Received GoPay notification: {notification_data}")

        # Extract payment ID
        payment_id = notification_data.get('id')
        if not payment_id:
            logger.error("No payment ID in notification")
            return HttpResponse("Invalid notification", status=400)

        # Find payment record
        try:
            payment = Payment.objects.get(gopay_payment_id=payment_id)
        except Payment.DoesNotExist:
            logger.error(f"Payment not found: {payment_id}")
            return HttpResponse("Payment not found", status=404)

        # Store notification
        notification = PaymentNotification.objects.create(
            payment=payment,
            gopay_payment_id=payment_id,
            notification_data=json.dumps(notification_data)
        )

        # Verify notification (optional - implement based on GoPay docs)
        gopay_service = GoPayService()
        # is_valid = gopay_service.verify_notification(notification_data, signature)
        # For now, we'll process all notifications
        is_valid = True

        if is_valid:
            # Process the notification
            process_payment_notification(payment, notification_data)
            notification.processed = True
            notification.save()

            return HttpResponse("OK", status=200)
        else:
            logger.warning(f"Invalid notification signature for payment {payment_id}")
            return HttpResponse("Invalid signature", status=400)

    except Exception as e:
        logger.error(f"Error processing GoPay notification: {e}")
        return HttpResponse("Error processing notification", status=500)


def process_payment_notification(payment, notification_data):
    """Process GoPay payment notification"""
    try:
        # Update payment status
        payment.gopay_state = notification_data.get('state')
        payment.gopay_sub_state = notification_data.get('sub_state')
        payment.gopay_payment_instrument = notification_data.get('payment_instrument')

        # Check if payment is successful
        if notification_data.get('state') == 'PAID':
            payment.status = 'paid'
            payment.paid_at = timezone.now()

            # Process successful payment with Uscreen
            if not payment.uscreen_access_granted:
                process_successful_payment_with_uscreen(payment)

        elif notification_data.get('state') in ['CANCELED', 'TIMEOUTED']:
            payment.status = 'cancelled'

        elif notification_data.get('state') == 'REFUNDED':
            payment.status = 'refunded'
            payment.uscreen_access_granted = False

        payment.save()

        logger.info(f"Updated payment {payment.id} status to {payment.status}")

    except Exception as e:
        logger.error(f"Error processing payment notification: {e}")
        raise


def process_successful_payment_with_uscreen(payment):
    """Process successful payment with Uscreen integration"""
    try:
        uscreen_service = UscreenService()

        # Process payment with Uscreen
        result = uscreen_service.process_successful_payment(
            email=payment.email,
            event_id=payment.event_id,
            payment_id=payment.gopay_payment_id
        )

        # Update payment record
        payment.uscreen_user = result['uscreen_user']
        payment.uscreen_program = result['uscreen_program']
        payment.uscreen_access_granted = result['access_granted']
        payment.save()

        logger.info(f"Successfully processed Uscreen integration for payment {payment.id}")

        # Send confirmation email
        try:
            from .services.email_service import EmailService
            email_service = EmailService()
            email_service.send_payment_confirmation(
                payment=payment,
                uscreen_user_data=result.get('user_data')
            )
            logger.info(f"Payment confirmation email sent for payment {payment.id}")
        except Exception as e:
            logger.error(f"Failed to send payment confirmation email: {e}")
            # Don't fail the whole process if email fails

    except Exception as e:
        logger.error(f"Error processing Uscreen integration: {e}")
        # Don't raise exception - payment is still successful even if Uscreen fails
        # This should be handled separately (retry mechanism, manual processing, etc.)


@api_view(['GET'])
def payment_status(request, payment_id):
    """
    Get payment status

    GET /api/payment-status/{payment_id}/
    """
    try:
        payment = Payment.objects.get(gopay_payment_id=payment_id)

        return Response({
            'payment_id': payment.gopay_payment_id,
            'status': payment.status,
            'email': payment.email,
            'event_id': payment.event_id,
            'price': str(payment.price),
            'created_at': payment.created_at,
            'paid_at': payment.paid_at,
            'uscreen_access_granted': payment.uscreen_access_granted
        })

    except Payment.DoesNotExist:
        return Response({
            'error': 'Payment not found'
        }, status=status.HTTP_404_NOT_FOUND)


@csrf_exempt
@api_view(['POST'])
def check_user_exists(request):
    """
    Check if user exists in Uscreen

    POST /api/check-user/
    {
        "email": "<EMAIL>"
    }
    """
    try:
        email = request.data.get('email')

        if not email:
            return Response({
                'error': 'Email is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Try to check in Uscreen
        try:
            uscreen_service = UscreenService()
            user_data = uscreen_service.get_user_by_email(email)

            if user_data:
                return Response({
                    'exists': True,
                    'user_id': user_data['id'],
                    'name': user_data.get('name'),
                    'email': user_data['email'],
                    'source': 'uscreen'
                })
        except Exception as e:
            logger.warning(f"Uscreen API not available for user check: {e}")

        # Return mock response when Uscreen is not available
        return Response({
            'exists': False,
            'message': 'User will be created automatically after payment',
            'source': 'mock'
        })

    except Exception as e:
        logger.error(f"Error checking user existence: {e}")
        return Response({
            'error': f'User check failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
def create_payment_with_user_check(request):
    """
    Enhanced payment creation with user verification

    POST /api/create-payment-enhanced/
    {
        "email": "<EMAIL>",
        "event_id": "event-slug-123",
        "price": 299.00,
        "user_name": "John Doe" (optional),
        "check_user": true (optional)
    }
    """
    try:
        data = request.data

        # Validate required fields
        email = data.get('email')
        event_id = data.get('event_id')
        price = data.get('price')
        user_name = data.get('user_name')
        check_user = data.get('check_user', False)

        if not all([email, event_id, price]):
            return Response({
                'error': 'Missing required fields: email, event_id, price'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Convert price to Decimal
        try:
            price = Decimal(str(price))
        except (ValueError, TypeError):
            return Response({
                'error': 'Invalid price format'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Optional: Check if user exists in Uscreen
        user_exists = False
        uscreen_user_data = None

        if check_user:
            try:
                uscreen_service = UscreenService()
                uscreen_user_data = uscreen_service.get_user_by_email(email)
                user_exists = uscreen_user_data is not None
            except Exception as e:
                logger.warning(f"Could not check user existence: {e}")

        # Initialize GoPay service
        gopay_service = GoPayService()

        # Create payment in GoPay
        gopay_response = gopay_service.create_payment(
            email=email,
            event_id=event_id,
            price=price
        )

        # Create local payment record
        payment = Payment.objects.create(
            email=email,
            event_id=event_id,
            gopay_payment_id=gopay_response['id'],
            price=price,
            gopay_order_number=gopay_response.get('order_number'),
            gopay_state=gopay_response.get('state'),
            gopay_sub_state=gopay_response.get('sub_state')
        )

        logger.info(f"Created enhanced payment {payment.id} for {email}")

        # Return payment URL for redirect with user info
        return Response({
            'payment_id': payment.gopay_payment_id,
            'payment_url': gopay_response['gw_url'],
            'status': 'created',
            'user_exists': user_exists,
            'user_data': uscreen_user_data if user_exists else None,
            'will_create_user': not user_exists
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error creating enhanced payment: {e}")
        return Response({
            'error': f'Payment creation failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def payment_success(request):
    """Success page after payment"""
    return render(request, 'payments/success.html')


def payment_error(request):
    """Error page for failed payments"""
    return render(request, 'payments/error.html')


def test_integration(request):
    """Test page for SportLive24 payment integration"""
    return render(request, 'payments/test_integration.html')


@api_view(['GET'])
def test_api_status(request):
    """Test API status and mock data"""
    return Response({
        'status': 'ok',
        'message': 'SportLive24 Payment API is running',
        'mock_products': {
            'heroes-gate-31': {
                'id': 12345,
                'name': 'Heroes Gate 31',
                'price': 299.00,
                'currency': 'CZK'
            },
            'ufc-300': {
                'id': 12346,
                'name': 'UFC 300 Championship',
                'price': 299.00,
                'currency': 'CZK'
            }
        },
        'endpoints': {
            'product_info': '/api/product/{slug}/',
            'create_payment': '/api/create-payment-from-product/',
            'check_user': '/api/check-user/',
            'test_page': '/api/test-integration/'
        }
    })


@api_view(['GET'])
def mock_product_info(request, product_slug):
    """Mock product info endpoint that works without Uscreen API"""
    mock_products = {
        'heroes-gate-31': {
            'id': 12345,
            'slug': 'heroes-gate-31',
            'name': 'Heroes Gate 31',
            'description': 'Exkluzivní PPV Event - MMA Championship',
            'price': 299.00,
            'currency': 'CZK',
            'available': True,
            'source': 'mock'
        },
        'ufc-300': {
            'id': 12346,
            'slug': 'ufc-300',
            'name': 'UFC 300 Championship',
            'description': 'Ultimate Fighting Championship 300',
            'price': 299.00,
            'currency': 'CZK',
            'available': True,
            'source': 'mock'
        }
    }

    if product_slug in mock_products:
        return Response(mock_products[product_slug])
    else:
        return Response({
            'error': 'Product not found'
        }, status=status.HTTP_404_NOT_FOUND)


@csrf_exempt
@api_view(['POST'])
def mock_create_payment(request):
    """Mock payment creation endpoint for testing"""
    try:
        data = request.data
        email = data.get('email', '<EMAIL>')
        product_slug = data.get('product_slug', 'heroes-gate-31')
        user_name = data.get('user_name', 'Test User')

        # Mock payment response
        mock_payment_id = f"MOCK_{timezone.now().strftime('%Y%m%d%H%M%S')}"

        return Response({
            'payment_id': mock_payment_id,
            'payment_url': f'https://gw.sandbox.gopay.com/mock-payment/{mock_payment_id}',
            'status': 'created',
            'product_info': {
                'name': 'Heroes Gate 31',
                'price': 299.0,
                'currency': 'CZK'
            },
            'user_exists': False,
            'user_data': None,
            'will_create_user': True,
            'source': 'mock'
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': f'Mock payment creation failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
def debug_endpoint(request):
    """Debug endpoint to check what's happening"""
    return Response({
        'method': request.method,
        'path': request.path,
        'data': request.data if request.method == 'POST' else None,
        'query_params': dict(request.query_params),
        'headers': dict(request.headers),
        'timestamp': timezone.now().isoformat(),
        'message': 'Debug endpoint is working'
    })


@api_view(['GET'])
def get_product_info(request, product_slug):
    """
    Get product information from Uscreen by slug

    GET /api/product/{product_slug}/
    """
    try:
        # Mock data for testing when Uscreen API is not available
        mock_products = {
            'heroes-gate-31': {
                'id': 12345,
                'slug': 'heroes-gate-31',
                'name': 'Heroes Gate 31',
                'description': 'Exkluzivní PPV Event - MMA Championship',
                'price': 29900,  # in cents
            },
            'ufc-300': {
                'id': 12346,
                'slug': 'ufc-300',
                'name': 'UFC 300 Championship',
                'description': 'Ultimate Fighting Championship 300',
                'price': 29900,  # in cents
            }
        }

        # Try to get from Uscreen first
        try:
            uscreen_service = UscreenService()
            product_data = uscreen_service.get_product_by_slug(product_slug)

            if product_data:
                # Convert price from cents to CZK
                price_czk = product_data.get('price', 0) / 100

                return Response({
                    'product_id': product_data['id'],
                    'slug': product_data.get('slug', product_slug),
                    'name': product_data.get('name', ''),
                    'description': product_data.get('description', ''),
                    'price': price_czk,
                    'currency': 'CZK',
                    'available': True,
                    'source': 'uscreen'
                })
        except Exception as e:
            logger.warning(f"Uscreen API not available, using mock data: {e}")

        # Fall back to mock data
        if product_slug in mock_products:
            mock_data = mock_products[product_slug]
            price_czk = mock_data['price'] / 100

            return Response({
                'product_id': mock_data['id'],
                'slug': mock_data['slug'],
                'name': mock_data['name'],
                'description': mock_data['description'],
                'price': price_czk,
                'currency': 'CZK',
                'available': True,
                'source': 'mock'
            })

        return Response({
            'error': 'Product not found'
        }, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        logger.error(f"Error getting product info: {e}")
        return Response({
            'error': f'Product lookup failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
def create_payment_from_uscreen_product(request):
    """
    Create payment directly from Uscreen product page

    POST /api/create-payment-from-product/
    {
        "email": "<EMAIL>",
        "product_slug": "heroes-gate-31",
        "user_name": "John Doe" (optional),
        "return_url": "https://sportlive24.tv/programs/heroes-gate-31" (optional)
    }
    """
    try:
        data = request.data

        # Validate required fields
        email = data.get('email')
        product_slug = data.get('product_slug')
        user_name = data.get('user_name')
        return_url = data.get('return_url')

        if not all([email, product_slug]):
            return Response({
                'error': 'Missing required fields: email, product_slug'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Mock data for testing when Uscreen API is not available
        mock_products = {
            'heroes-gate-31': {
                'id': 12345,
                'slug': 'heroes-gate-31',
                'name': 'Heroes Gate 31',
                'description': 'Exkluzivní PPV Event - MMA Championship',
                'price': 29900,  # in cents
            },
            'ufc-300': {
                'id': 12346,
                'slug': 'ufc-300',
                'name': 'UFC 300 Championship',
                'description': 'Ultimate Fighting Championship 300',
                'price': 29900,  # in cents
            }
        }

        # Try to get product information from Uscreen first
        product_data = None
        try:
            uscreen_service = UscreenService()
            product_data = uscreen_service.get_product_by_slug(product_slug)
        except Exception as e:
            logger.warning(f"Uscreen API not available, using mock data: {e}")

        # Fall back to mock data if Uscreen is not available
        if not product_data and product_slug in mock_products:
            product_data = mock_products[product_slug]
            logger.info(f"Using mock data for product: {product_slug}")

        if not product_data:
            return Response({
                'error': f'Product not found: {product_slug}'
            }, status=status.HTTP_404_NOT_FOUND)

        # Convert price from cents to CZK
        price_czk = Decimal(str(product_data.get('price', 0))) / 100

        if price_czk <= 0:
            return Response({
                'error': 'Invalid product price'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if user exists in Uscreen
        user_exists = False
        uscreen_user_data = None

        try:
            uscreen_user_data = uscreen_service.get_user_by_email(email)
            user_exists = uscreen_user_data is not None
        except Exception as e:
            logger.warning(f"Could not check user existence: {e}")

        # Initialize GoPay service
        gopay_service = GoPayService()

        # Create payment in GoPay using product_slug as event_id
        gopay_response = gopay_service.create_payment(
            email=email,
            event_id=product_slug,
            price=price_czk
        )

        # Create local payment record
        payment = Payment.objects.create(
            email=email,
            event_id=product_slug,
            gopay_payment_id=gopay_response['id'],
            price=price_czk,
            gopay_order_number=gopay_response.get('order_number'),
            gopay_state=gopay_response.get('state'),
            gopay_sub_state=gopay_response.get('sub_state')
        )

        logger.info(f"Created payment {payment.id} for product {product_slug} by {email}")

        # Return payment URL and additional info
        return Response({
            'payment_id': payment.gopay_payment_id,
            'payment_url': gopay_response['gw_url'],
            'status': 'created',
            'product_info': {
                'name': product_data.get('name', ''),
                'price': float(price_czk),
                'currency': 'CZK'
            },
            'user_exists': user_exists,
            'user_data': uscreen_user_data if user_exists else None,
            'will_create_user': not user_exists
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error creating payment from product: {e}")
        return Response({
            'error': f'Payment creation failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
