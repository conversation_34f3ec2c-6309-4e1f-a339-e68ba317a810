"""
Quick fix views that work without restart
"""
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
import json
import logging

logger = logging.getLogger(__name__)


@csrf_exempt
@require_http_methods(["POST"])
def quick_test_payment(request):
    """Quick test payment endpoint that works without restart"""
    try:
        # Parse JSON data
        data = json.loads(request.body.decode('utf-8'))
        
        email = data.get('email', '<EMAIL>')
        product_slug = data.get('product_slug', 'heroes-gate-31')
        user_name = data.get('user_name', 'Test User')
        
        # Mock payment response
        mock_payment_id = f"QUICK_{timezone.now().strftime('%Y%m%d%H%M%S')}"
        
        response_data = {
            'payment_id': mock_payment_id,
            'payment_url': f'https://gw.sandbox.gopay.com/quick-test/{mock_payment_id}',
            'status': 'created',
            'product_info': {
                'name': 'Heroes Gate 31',
                'price': 299.0,
                'currency': 'CZK'
            },
            'user_exists': False,
            'user_data': None,
            'will_create_user': True,
            'source': 'quick_fix',
            'message': 'This is a quick fix endpoint that works without restart'
        }
        
        logger.info(f"Quick test payment created: {mock_payment_id} for {email}")
        
        return JsonResponse(response_data, status=201)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        logger.error(f"Quick test payment error: {e}")
        return JsonResponse({
            'error': f'Quick test payment failed: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def quick_test_status(request):
    """Quick test status endpoint"""
    return JsonResponse({
        'status': 'ok',
        'message': 'Quick fix endpoint is working',
        'timestamp': timezone.now().isoformat(),
        'endpoints': {
            'quick_test_payment': '/api/quick-test-payment/',
            'quick_test_status': '/api/quick-test-status/'
        }
    })


@csrf_exempt
@require_http_methods(["GET"])
def quick_product_info(request, product_slug):
    """Quick product info endpoint"""
    mock_products = {
        'heroes-gate-31': {
            'id': 12345,
            'slug': 'heroes-gate-31',
            'name': 'Heroes Gate 31',
            'description': 'Exkluzivní PPV Event - MMA Championship',
            'price': 299.0,
            'currency': 'CZK',
            'available': True,
            'source': 'quick_fix'
        },
        'ufc-300': {
            'id': 12346,
            'slug': 'ufc-300',
            'name': 'UFC 300 Championship',
            'description': 'Ultimate Fighting Championship 300',
            'price': 299.0,
            'currency': 'CZK',
            'available': True,
            'source': 'quick_fix'
        }
    }
    
    if product_slug in mock_products:
        return JsonResponse(mock_products[product_slug])
    else:
        return JsonResponse({
            'error': 'Product not found'
        }, status=404)
