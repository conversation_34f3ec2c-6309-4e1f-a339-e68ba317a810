import requests
import json
from django.conf import settings
from django.utils import timezone
from ..models import UscreenUser, UscreenProgram
import logging

logger = logging.getLogger(__name__)


class UscreenService:
    """Service for Uscreen API integration"""

    def __init__(self):
        self.api_key = settings.USCREEN_API_KEY
        self.api_url = settings.USCREEN_API_URL
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
        }

    def get_user_by_email(self, email):
        """Get user from Uscreen by email"""
        url = f"{self.api_url}/users"
        params = {'email': email}

        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            users_data = response.json()

            if users_data.get('data') and len(users_data['data']) > 0:
                user_data = users_data['data'][0]
                logger.info(f"Found Uscreen user: {email}")
                return user_data
            else:
                logger.info(f"Uscreen user not found: {email}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get Uscreen user: {e}")
            raise Exception(f"Uscreen user lookup failed: {e}")

    def create_user(self, email, name=None):
        """Create a new user in Uscreen"""
        url = f"{self.api_url}/users"

        # If name is not provided, extract from email
        if not name:
            name = email.split('@')[0]

        user_data = {
            'email': email,
            'name': name,
            'password': self._generate_random_password(),
            'send_welcome_email': True
        }

        try:
            response = requests.post(url, headers=self.headers, json=user_data)
            response.raise_for_status()

            created_user = response.json()

            logger.info(f"Successfully created Uscreen user: {email}")
            return created_user

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create Uscreen user: {e}")
            raise Exception(f"Uscreen user creation failed: {e}")

    def get_or_create_user(self, email, name=None):
        """Get existing user or create new one"""
        # First try to get existing user
        user_data = self.get_user_by_email(email)

        if user_data:
            # Update or create local UscreenUser record
            uscreen_user, created = UscreenUser.objects.get_or_create(
                uscreen_id=user_data['id'],
                defaults={
                    'email': user_data['email'],
                    'name': user_data.get('name', email.split('@')[0])
                }
            )

            if not created:
                # Update existing record
                uscreen_user.email = user_data['email']
                uscreen_user.name = user_data.get('name', email.split('@')[0])
                uscreen_user.save()

            return uscreen_user, user_data
        else:
            # Create new user
            user_data = self.create_user(email, name)

            # Create local UscreenUser record
            uscreen_user = UscreenUser.objects.create(
                uscreen_id=user_data['id'],
                email=user_data['email'],
                name=user_data.get('name', email.split('@')[0])
            )

            return uscreen_user, user_data

    def get_product_by_slug(self, event_slug):
        """Get product/program from Uscreen by slug or ID"""
        url = f"{self.api_url}/products"
        params = {'slug': event_slug}

        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()

            products_data = response.json()

            if products_data.get('data') and len(products_data['data']) > 0:
                product_data = products_data['data'][0]
                logger.info(f"Found Uscreen product: {event_slug}")
                return product_data
            else:
                logger.warning(f"Uscreen product not found: {event_slug}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get Uscreen product: {e}")
            raise Exception(f"Uscreen product lookup failed: {e}")

    def grant_access_to_product(self, user_id, product_id):
        """Grant user access to a specific product (PPV)"""
        url = f"{self.api_url}/users/{user_id}/accesses"

        access_data = {
            'product_id': product_id,
            'access_type': 'purchase',  # or 'rental' depending on your setup
            'expires_at': None  # Set expiration if needed
        }

        try:
            response = requests.post(url, headers=self.headers, json=access_data)
            response.raise_for_status()

            access_response = response.json()

            logger.info(f"Successfully granted access to product {product_id} for user {user_id}")
            return access_response

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to grant Uscreen access: {e}")
            raise Exception(f"Uscreen access grant failed: {e}")

    def process_successful_payment(self, email, event_id, payment_id):
        """Process successful payment - create user and grant access"""
        try:
            # Get or create user
            uscreen_user, user_data = self.get_or_create_user(email)
            user_was_created = not UscreenUser.objects.filter(email=email).exists()

            # Get product information
            product_data = self.get_product_by_slug(event_id)

            if not product_data:
                raise Exception(f"Product not found for event: {event_id}")

            # Update or create local UscreenProgram record
            uscreen_program, created = UscreenProgram.objects.get_or_create(
                uscreen_product_id=product_data['id'],
                defaults={
                    'event_slug': event_id,
                    'event_name': product_data.get('name', event_id),
                    'price': product_data.get('price', 0) / 100  # Convert from cents
                }
            )

            # Grant access to the product
            access_response = self.grant_access_to_product(
                user_data['id'],
                product_data['id']
            )

            logger.info(f"Successfully processed payment {payment_id} for {email}")

            # Send appropriate email notification
            try:
                from .email_service import EmailService
                email_service = EmailService()

                if user_was_created:
                    # Send welcome email for new users
                    email_service.send_new_user_welcome(
                        email=email,
                        uscreen_user_data=user_data,
                        event_name=product_data.get('name', event_id)
                    )
                    logger.info(f"Welcome email sent to new user {email}")
                else:
                    logger.info(f"User {email} already existed, no welcome email sent")

            except Exception as e:
                logger.error(f"Failed to send email notification: {e}")
                # Don't fail the whole process if email fails

            return {
                'uscreen_user': uscreen_user,
                'uscreen_program': uscreen_program,
                'access_granted': True,
                'access_response': access_response,
                'user_was_created': user_was_created,
                'user_data': user_data
            }

        except Exception as e:
            logger.error(f"Failed to process successful payment: {e}")
            raise

    def _generate_random_password(self):
        """Generate a random password for new users"""
        import secrets
        import string

        alphabet = string.ascii_letters + string.digits
        password = ''.join(secrets.choice(alphabet) for i in range(12))
        return password
