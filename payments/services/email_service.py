from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
import logging

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending email notifications"""

    def __init__(self):
        self.from_email = settings.DEFAULT_FROM_EMAIL

    def send_payment_confirmation(self, payment, uscreen_user_data=None):
        """Send payment confirmation email"""
        try:
            subject = f"SportLive24 - Potvrzení platby za {payment.event_id}"

            context = {
                'payment': payment,
                'event_name': payment.event_id,
                'price': payment.price,
                'currency': payment.currency,
                'uscreen_user': uscreen_user_data,
                'access_granted': payment.uscreen_access_granted,
                'base_url': settings.BASE_URL
            }

            # Render HTML email
            html_message = render_to_string('emails/payment_confirmation.html', context)
            plain_message = strip_tags(html_message)

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=self.from_email,
                recipient_list=[payment.email],
                html_message=html_message,
                fail_silently=False
            )

            logger.info(f"Payment confirmation email sent to {payment.email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send payment confirmation email: {e}")
            return False

    def send_new_user_welcome(self, email, uscreen_user_data, event_name):
        """Send welcome email to new Uscreen users"""
        try:
            subject = "Vítejte na SportLive24 - Váš účet byl vytvořen"

            context = {
                'email': email,
                'user_name': uscreen_user_data.get('name', email.split('@')[0]),
                'event_name': event_name,
                'uscreen_login_url': 'https://sportlive24.tv/sign_in',
                'base_url': settings.BASE_URL
            }

            # Render HTML email
            html_message = render_to_string('emails/new_user_welcome.html', context)
            plain_message = strip_tags(html_message)

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=self.from_email,
                recipient_list=[email],
                html_message=html_message,
                fail_silently=False
            )

            logger.info(f"Welcome email sent to new user {email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send welcome email: {e}")
            return False

    def send_payment_failed_notification(self, email, event_id, reason=""):
        """Send payment failed notification"""
        try:
            subject = f"SportLive24 - Platba se nezdařila pro {event_id}"

            context = {
                'email': email,
                'event_id': event_id,
                'reason': reason,
                'support_email': '<EMAIL>',
                'base_url': settings.BASE_URL
            }

            # Render HTML email
            html_message = render_to_string('emails/payment_failed.html', context)
            plain_message = strip_tags(html_message)

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=self.from_email,
                recipient_list=[email],
                html_message=html_message,
                fail_silently=False
            )

            logger.info(f"Payment failed notification sent to {email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send payment failed notification: {e}")
            return False
