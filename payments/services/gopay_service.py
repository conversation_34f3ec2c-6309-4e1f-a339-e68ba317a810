import gopay
from gopay.enums import TokenScope, Language, PaymentInstrument
from datetime import datetime
from django.conf import settings
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


class GoPayService:
    """Service for GoPay API integration using official GoPay SDK"""

    def __init__(self):
        self.goid = settings.GOPAY_GOID
        self.client_id = settings.GOPAY_CLIENT_ID
        self.client_secret = settings.GOPAY_CLIENT_SECRET
        self.gateway_url = settings.GOPAY_GATEWAY_URL

        # Initialize GoPay payments client
        self.payments = gopay.payments({
            "goid": self.goid,
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "gateway_url": self.gateway_url,
            "scope": TokenScope.ALL,
            "language": Language.CZECH
        })

    def create_payment(self, email, event_id, price, currency='CZK'):
        """Create a new payment in GoPay using official SDK"""

        # Generate unique order number
        order_number = f"SL24-{event_id}-{int(datetime.now().timestamp())}"

        # Convert price to cents (GoPay expects amount in smallest currency unit)
        amount_in_cents = int(price * 100)

        payment_data = {
            "payer": {
                "default_payment_instrument": PaymentInstrument.BANK_ACCOUNT,
                "allowed_payment_instruments": [
                    PaymentInstrument.BANK_ACCOUNT,
                    PaymentInstrument.PAYMENT_CARD
                ],
                "contact": {
                    "email": email
                }
            },
            "amount": amount_in_cents,
            "currency": "CZK" if currency == 'CZK' else currency,
            "order_number": order_number,
            "order_description": f"SportLive24 - PPV Event: {event_id}",
            "items": [
                {
                    "name": f"PPV Event: {event_id}",
                    "amount": amount_in_cents,
                    "count": 1
                }
            ],
            "callback": {
                "return_url": f"{settings.BASE_URL}/payment-success/",
                "notification_url": f"{settings.BASE_URL}/api/notify/"
            },
            "additional_params": [
                {
                    "name": "event_id",
                    "value": event_id
                },
                {
                    "name": "email",
                    "value": email
                }
            ]
        }

        try:
            response = self.payments.create_payment(payment_data)

            if response.success:
                logger.info(f"Successfully created GoPay payment: {response.json.get('id')}")
                return response.json
            else:
                logger.error(f"Failed to create GoPay payment: {response.status_code} - {response.json}")
                raise Exception(f"Payment creation failed: {response.json}")

        except Exception as e:
            logger.error(f"Failed to create GoPay payment: {e}")
            raise Exception(f"Payment creation failed: {e}")

    def get_payment_status(self, payment_id):
        """Get payment status from GoPay using official SDK"""

        try:
            response = self.payments.get_status(payment_id)

            if response.success:
                logger.info(f"Retrieved GoPay payment status: {payment_id}")
                return response.json
            else:
                logger.error(f"Failed to get GoPay payment status: {response.status_code} - {response.json}")
                raise Exception(f"Payment status retrieval failed: {response.json}")

        except Exception as e:
            logger.error(f"Failed to get GoPay payment status: {e}")
            raise Exception(f"Payment status retrieval failed: {e}")

    def refund_payment(self, payment_id, amount=None):
        """Refund a payment using official SDK"""

        try:
            if amount:
                # Partial refund
                amount_in_cents = int(amount * 100)
                response = self.payments.refund_payment(payment_id, amount_in_cents)
            else:
                # Full refund
                response = self.payments.refund_payment(payment_id)

            if response.success:
                logger.info(f"Successfully refunded GoPay payment: {payment_id}")
                return response.json
            else:
                logger.error(f"Failed to refund GoPay payment: {response.status_code} - {response.json}")
                raise Exception(f"Payment refund failed: {response.json}")

        except Exception as e:
            logger.error(f"Failed to refund GoPay payment: {e}")
            raise Exception(f"Payment refund failed: {e}")

    def verify_notification(self, notification_data, signature=None):
        """
        Verify GoPay webhook notification
        Note: Official SDK doesn't provide signature verification method yet,
        so this is a placeholder for future implementation
        """
        try:
            # For now, we'll accept all notifications from GoPay
            # In production, you should implement proper signature verification
            # according to GoPay documentation

            logger.info("GoPay notification received (signature verification not implemented)")
            return True

        except Exception as e:
            logger.error(f"Error verifying GoPay notification: {e}")
            return False
