from django.db import models
from django.utils import timezone
import pytz


class UscreenUser(models.Model):
    """Model for Uscreen users"""
    uscreen_id = models.IntegerField(unique=True)
    email = models.EmailField()
    name = models.CharField(max_length=255)
    subscriber = models.BooleanField(default=False)
    referrer = models.CharField(max_length=255, null=True, blank=True)
    custom_fields = models.JSONField(null=True, blank=True)
    utm_params = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"UscreenUser {self.uscreen_id} - {self.name} ({self.email})"

    class Meta:
        db_table = 'uscreen_users'


class UscreenProgram(models.Model):
    """Model for Uscreen PPV programs/events"""
    uscreen_id = models.IntegerField(unique=True)
    title = models.Char<PERSON><PERSON>(max_length=255)
    description = models.TextField(blank=True, null=True)
    slug = models.CharField(max_length=255, unique=True, null=True, blank=True)

    # Program specific fields
    part_of_subscription = models.BooleanField(null=True, blank=True)
    main_poster_url = models.URLField(max_length=500, blank=True, null=True)
    secondary_poster_url = models.URLField(max_length=500, blank=True, null=True)
    price_cents = models.IntegerField(null=True, blank=True)
    rental_price_cents = models.IntegerField(null=True, blank=True)
    rental_period = models.CharField(max_length=50, null=True, blank=True)
    rental_period_text = models.CharField(max_length=50, null=True, blank=True)
    content_type = models.CharField(max_length=50, blank=True, null=True)
    event_launch_date = models.DateTimeField(null=True, blank=True)
    event_state = models.CharField(max_length=50, blank=True, null=True)

    # Author information
    author_name = models.CharField(max_length=255, blank=True, null=True)
    author_description = models.TextField(blank=True, null=True)
    author_image = models.URLField(max_length=500, blank=True, null=True)

    # Additional fields
    preregistration_screen_text = models.TextField(blank=True, null=True)
    subscription_plans = models.JSONField(null=True, blank=True)
    tags = models.JSONField(null=True, blank=True)

    # Timestamps
    uscreen_created_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def price_czk(self):
        """Convert price from cents to CZK"""
        if self.price_cents:
            return self.price_cents / 100
        return 0

    def __str__(self):
        return f"Program {self.slug or self.uscreen_id} - {self.title}"

    class Meta:
        db_table = 'uscreen_programs'


class UscreenOffer(models.Model):
    """Model for Uscreen offers"""
    uscreen_id = models.IntegerField(unique=True)
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    slug = models.CharField(max_length=255, unique=True, null=True, blank=True)

    # Offer specific fields
    offer_url = models.URLField(max_length=500, blank=True, null=True)
    private = models.BooleanField(null=True, blank=True)
    price_cents = models.IntegerField(null=True, blank=True)
    offer_type = models.CharField(max_length=50, blank=True, null=True)
    image_url = models.URLField(max_length=500, blank=True, null=True)

    # Timestamps
    uscreen_created_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def price_czk(self):
        """Convert price from cents to CZK"""
        if self.price_cents:
            return self.price_cents / 100
        return 0

    def __str__(self):
        return f"Offer {self.uscreen_id} - {self.title}"

    class Meta:
        db_table = 'uscreen_offers'


class UscreenVideo(models.Model):
    """Model for Uscreen videos"""
    uscreen_id = models.IntegerField(unique=True)
    title = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(blank=True, null=True)
    slug = models.CharField(max_length=255, unique=True, null=True, blank=True)

    # Video specific fields
    filename = models.CharField(max_length=255, blank=True, null=True)
    size = models.IntegerField(null=True, blank=True)  # Size in bytes
    duration = models.IntegerField(null=True, blank=True)  # Duration in seconds
    source = models.CharField(max_length=50, blank=True, null=True)
    image_url = models.URLField(max_length=500, blank=True, null=True)
    chapter_ids = models.JSONField(null=True, blank=True)

    # Timestamps
    uscreen_created_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title or self.filename or f"Video {self.uscreen_id}"

    class Meta:
        db_table = 'uscreen_videos'


class UscreenInvoice(models.Model):
    """Model for Uscreen invoices"""
    uscreen_id = models.IntegerField(unique=True)
    status = models.CharField(max_length=50)
    amount = models.IntegerField()  # Amount in cents
    currency = models.CharField(max_length=10)
    user_id = models.IntegerField()
    product_id = models.IntegerField()
    product_type = models.CharField(max_length=50)

    # Timestamps
    paid_at = models.DateTimeField(null=True, blank=True)
    uscreen_created_at = models.DateTimeField(null=True, blank=True)
    uscreen_updated_at = models.DateTimeField(null=True, blank=True)

    # Additional fields
    discount = models.IntegerField(default=0)
    trial = models.BooleanField(default=False)
    gift_amount = models.IntegerField(default=0)
    origin = models.CharField(max_length=50, blank=True, null=True)
    coupon = models.CharField(max_length=50, null=True, blank=True)

    # Localized amounts
    localized_currency = models.CharField(max_length=10, null=True, blank=True)
    localized_amount = models.IntegerField(null=True, blank=True)
    localized_discount = models.IntegerField(null=True, blank=True)
    localized_gift_amount = models.IntegerField(null=True, blank=True)

    # Local timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def amount_czk(self):
        """Convert amount from cents to CZK"""
        return self.amount / 100

    def __str__(self):
        return f"Invoice {self.uscreen_id} - {self.status} - {self.amount_czk} CZK"

    class Meta:
        db_table = 'uscreen_invoices'


class UscreenDataSync(models.Model):
    """Model to track data synchronization from Uscreen"""
    SYNC_TYPES = [
        ('programs', 'Programs'),
        ('offers', 'Offers'),
        ('videos', 'Videos'),
        ('users', 'Users'),
        ('invoices', 'Invoices'),
    ]

    sync_type = models.CharField(max_length=20, choices=SYNC_TYPES)
    last_sync = models.DateTimeField(default=timezone.now)
    success = models.BooleanField(default=True)
    records_processed = models.IntegerField(default=0)
    error_message = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        status = "✅" if self.success else "❌"
        return f"{status} {self.sync_type} - {self.last_sync.strftime('%Y-%m-%d %H:%M')} ({self.records_processed} records)"

    class Meta:
        db_table = 'uscreen_data_sync'
        ordering = ['-last_sync']


class Payment(models.Model):
    """Model for GoPay payments"""
    STATUS_CHOICES = [
        ('created', 'Created'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]

    email = models.EmailField()
    event_id = models.CharField(max_length=255)
    gopay_payment_id = models.CharField(max_length=255, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='created')
    price = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='CZK')

    # GoPay specific fields
    gopay_order_number = models.CharField(max_length=255, null=True, blank=True)
    gopay_state = models.CharField(max_length=50, null=True, blank=True)
    gopay_sub_state = models.CharField(max_length=50, null=True, blank=True)
    gopay_payment_instrument = models.CharField(max_length=50, null=True, blank=True)

    # Uscreen integration
    uscreen_user = models.ForeignKey(UscreenUser, on_delete=models.SET_NULL, null=True, blank=True)
    uscreen_program = models.ForeignKey(UscreenProgram, on_delete=models.SET_NULL, null=True, blank=True)
    uscreen_offer = models.ForeignKey(UscreenOffer, on_delete=models.SET_NULL, null=True, blank=True)
    uscreen_video = models.ForeignKey(UscreenVideo, on_delete=models.SET_NULL, null=True, blank=True)
    uscreen_access_granted = models.BooleanField(default=False)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    paid_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Payment {self.gopay_payment_id} - {self.email} - {self.status}"

    class Meta:
        db_table = 'payments'


class PaymentNotification(models.Model):
    """Model for storing GoPay webhook notifications"""
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='notifications')
    gopay_payment_id = models.CharField(max_length=255)
    notification_data = models.TextField()
    processed = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"Notification for {self.gopay_payment_id} - {'Processed' if self.processed else 'Pending'}"

    class Meta:
        db_table = 'payment_notifications'
