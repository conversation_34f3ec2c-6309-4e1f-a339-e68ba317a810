# Generated by Django 4.2.21 on 2025-05-28 10:30

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UscreenDataSync',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sync_type', models.CharField(choices=[('programs', 'Programs'), ('offers', 'Offers'), ('videos', 'Videos'), ('users', 'Users'), ('invoices', 'Invoices')], max_length=20)),
                ('last_sync', models.DateTimeField(default=django.utils.timezone.now)),
                ('success', models.BooleanField(default=True)),
                ('records_processed', models.IntegerField(default=0)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'uscreen_data_sync',
                'ordering': ['-last_sync'],
            },
        ),
        migrations.CreateModel(
            name='UscreenInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uscreen_id', models.IntegerField(unique=True)),
                ('status', models.CharField(max_length=50)),
                ('amount', models.IntegerField()),
                ('currency', models.CharField(max_length=10)),
                ('user_id', models.IntegerField()),
                ('product_id', models.IntegerField()),
                ('product_type', models.CharField(max_length=50)),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
                ('uscreen_created_at', models.DateTimeField(blank=True, null=True)),
                ('uscreen_updated_at', models.DateTimeField(blank=True, null=True)),
                ('discount', models.IntegerField(default=0)),
                ('trial', models.BooleanField(default=False)),
                ('gift_amount', models.IntegerField(default=0)),
                ('origin', models.CharField(blank=True, max_length=50, null=True)),
                ('coupon', models.CharField(blank=True, max_length=50, null=True)),
                ('localized_currency', models.CharField(blank=True, max_length=10, null=True)),
                ('localized_amount', models.IntegerField(blank=True, null=True)),
                ('localized_discount', models.IntegerField(blank=True, null=True)),
                ('localized_gift_amount', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'uscreen_invoices',
            },
        ),
        migrations.CreateModel(
            name='UscreenOffer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uscreen_id', models.IntegerField(unique=True)),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('offer_url', models.URLField(blank=True, max_length=500, null=True)),
                ('private', models.BooleanField(blank=True, null=True)),
                ('price_cents', models.IntegerField(blank=True, null=True)),
                ('offer_type', models.CharField(blank=True, max_length=50, null=True)),
                ('image_url', models.URLField(blank=True, max_length=500, null=True)),
                ('uscreen_created_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'uscreen_offers',
            },
        ),
        migrations.CreateModel(
            name='UscreenVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uscreen_id', models.IntegerField(unique=True)),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('filename', models.CharField(blank=True, max_length=255, null=True)),
                ('size', models.IntegerField(blank=True, null=True)),
                ('duration', models.IntegerField(blank=True, null=True)),
                ('source', models.CharField(blank=True, max_length=50, null=True)),
                ('image_url', models.URLField(blank=True, max_length=500, null=True)),
                ('chapter_ids', models.JSONField(blank=True, null=True)),
                ('uscreen_created_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'uscreen_videos',
            },
        ),
        migrations.RenameField(
            model_name='uscreenprogram',
            old_name='event_name',
            new_name='title',
        ),
        migrations.RenameField(
            model_name='uscreenprogram',
            old_name='uscreen_product_id',
            new_name='uscreen_id',
        ),
        migrations.RemoveField(
            model_name='uscreenprogram',
            name='event_slug',
        ),
        migrations.RemoveField(
            model_name='uscreenprogram',
            name='price',
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='author_description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='author_image',
            field=models.URLField(blank=True, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='author_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='content_type',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='event_launch_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='event_state',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='main_poster_url',
            field=models.URLField(blank=True, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='part_of_subscription',
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='preregistration_screen_text',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='price_cents',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='rental_period',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='rental_period_text',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='rental_price_cents',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='secondary_poster_url',
            field=models.URLField(blank=True, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='slug',
            field=models.CharField(blank=True, max_length=255, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='subscription_plans',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='tags',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenprogram',
            name='uscreen_created_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenuser',
            name='custom_fields',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='uscreenuser',
            name='referrer',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='uscreenuser',
            name='subscriber',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='uscreenuser',
            name='utm_params',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='uscreen_offer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.uscreenoffer'),
        ),
        migrations.AddField(
            model_name='payment',
            name='uscreen_video',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.uscreenvideo'),
        ),
    ]
