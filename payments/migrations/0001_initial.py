# Generated by Django 4.2.21 on 2025-05-27 13:38

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('event_id', models.CharField(max_length=255)),
                ('gopay_payment_id', models.CharField(max_length=255, unique=True)),
                ('status', models.CharField(choices=[('created', 'Created'), ('paid', 'Paid'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], default='created', max_length=20)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.Char<PERSON>ield(default='CZK', max_length=3)),
                ('gopay_order_number', models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('gopay_state', models.CharField(blank=True, max_length=50, null=True)),
                ('gopay_sub_state', models.CharField(blank=True, max_length=50, null=True)),
                ('gopay_payment_instrument', models.CharField(blank=True, max_length=50, null=True)),
                ('uscreen_access_granted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'payments',
            },
        ),
        migrations.CreateModel(
            name='UscreenProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uscreen_product_id', models.IntegerField(unique=True)),
                ('event_slug', models.CharField(max_length=255, unique=True)),
                ('event_name', models.CharField(max_length=255)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'uscreen_programs',
            },
        ),
        migrations.CreateModel(
            name='UscreenUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uscreen_id', models.IntegerField(unique=True)),
                ('email', models.EmailField(max_length=254)),
                ('name', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'uscreen_users',
            },
        ),
        migrations.CreateModel(
            name='PaymentNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gopay_payment_id', models.CharField(max_length=255)),
                ('notification_data', models.TextField()),
                ('processed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='payments.payment')),
            ],
            options={
                'db_table': 'payment_notifications',
            },
        ),
        migrations.AddField(
            model_name='payment',
            name='uscreen_program',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.uscreenprogram'),
        ),
        migrations.AddField(
            model_name='payment',
            name='uscreen_user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.uscreenuser'),
        ),
    ]
