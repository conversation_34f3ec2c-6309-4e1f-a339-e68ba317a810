"""
Management command for hourly Uscreen data synchronization
Optimized for frequent updates - fetches only events data for last 2 hours
"""
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.utils import timezone
from payments.models import UscreenDataSync


class Command(BaseCommand):
    help = 'Hourly synchronization of Uscreen events data (programs, offers, videos) for last 2 hours.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--hours',
            type=int,
            default=2,
            help='Number of hours to look back for data (default: 2)'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output'
        )

    def handle(self, *args, **options):
        """Perform hourly synchronization of events data"""
        
        start_time = timezone.now()
        hours_back = options['hours']
        verbose = options['verbose']
        
        if verbose:
            self.stdout.write(
                self.style.SUCCESS(f"🕐 Starting hourly Uscreen sync at {start_time}")
            )
            self.stdout.write(f"📅 Fetching data from last {hours_back} hours")

        try:
            # Sync only events data (programs, offers, videos) - most frequently updated
            event_types = ['programs', 'offers', 'videos']
            total_processed = 0
            
            for event_type in event_types:
                if verbose:
                    self.stdout.write(f"🔄 Syncing {event_type}...")
                
                call_command('fetch_uscreen_data', type=event_type, hours=hours_back)
                
                # Get sync result
                latest_sync = UscreenDataSync.objects.filter(sync_type=event_type).order_by('-last_sync').first()
                if latest_sync:
                    total_processed += latest_sync.records_processed
                    if verbose:
                        status_icon = "✅" if latest_sync.success else "❌"
                        self.stdout.write(f"  {status_icon} {event_type}: {latest_sync.records_processed} records")

            end_time = timezone.now()
            duration = end_time - start_time
            
            if verbose:
                self.stdout.write("=" * 50)
                self.stdout.write(
                    self.style.SUCCESS(
                        f"✅ Hourly sync completed successfully!\n"
                        f"⏱️  Duration: {duration}\n"
                        f"📊 Total records processed: {total_processed}"
                    )
                )
            else:
                # Compact output for cron logs
                self.stdout.write(f"Hourly sync: {total_processed} records in {duration}")

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Hourly sync failed: {e}")
            )
            raise
