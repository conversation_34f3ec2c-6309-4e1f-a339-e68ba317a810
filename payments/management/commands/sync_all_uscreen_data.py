"""
Management command to perform full synchronization of all Uscreen data
"""
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.utils import timezone
from payments.models import UscreenDataSync


class Command(BaseCommand):
    help = 'Performs full synchronization of all Uscreen data (users, programs, offers, videos, invoices).'

    def add_arguments(self, parser):
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Number of hours to look back for data (default: 24)'
        )
        parser.add_argument(
            '--full-sync',
            action='store_true',
            help='Perform full synchronization (ignore time range)'
        )
        parser.add_argument(
            '--skip-invoices',
            action='store_true',
            help='Skip invoice synchronization (faster for testing)'
        )

    def handle(self, *args, **options):
        """Perform full synchronization"""
        
        start_time = timezone.now()
        
        self.stdout.write(
            self.style.SUCCESS(f"Starting full Uscreen data synchronization at {start_time}")
        )

        try:
            # Sync users first (needed for foreign key relationships)
            self.stdout.write("=" * 50)
            self.stdout.write("1. Synchronizing users...")
            call_command('fetch_uscreen_data', type='users', **options)

            # Sync programs
            self.stdout.write("=" * 50)
            self.stdout.write("2. Synchronizing programs...")
            call_command('fetch_uscreen_data', type='programs', **options)

            # Sync offers
            self.stdout.write("=" * 50)
            self.stdout.write("3. Synchronizing offers...")
            call_command('fetch_uscreen_data', type='offers', **options)

            # Sync videos
            self.stdout.write("=" * 50)
            self.stdout.write("4. Synchronizing videos...")
            call_command('fetch_uscreen_data', type='videos', **options)

            # Sync invoices (can be slow, so allow skipping)
            if not options['skip_invoices']:
                self.stdout.write("=" * 50)
                self.stdout.write("5. Synchronizing invoices...")
                call_command('fetch_uscreen_data', type='invoices', **options)
            else:
                self.stdout.write("=" * 50)
                self.stdout.write("5. Skipping invoices (--skip-invoices flag used)")

            end_time = timezone.now()
            duration = end_time - start_time

            self.stdout.write("=" * 50)
            self.stdout.write(
                self.style.SUCCESS(
                    f"Full synchronization completed successfully!\n"
                    f"Started: {start_time}\n"
                    f"Finished: {end_time}\n"
                    f"Duration: {duration}"
                )
            )

            # Show sync summary
            self.show_sync_summary()

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Full synchronization failed: {e}")
            )
            raise

    def show_sync_summary(self):
        """Show summary of recent synchronizations"""
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("SYNCHRONIZATION SUMMARY:")
        self.stdout.write("=" * 50)

        # Get recent sync results
        recent_syncs = UscreenDataSync.objects.filter(
            last_sync__gte=timezone.now() - timezone.timedelta(hours=1)
        ).order_by('sync_type', '-last_sync')

        if not recent_syncs.exists():
            self.stdout.write("No recent synchronizations found.")
            return

        # Group by sync type and show latest result
        sync_types = {}
        for sync in recent_syncs:
            if sync.sync_type not in sync_types:
                sync_types[sync.sync_type] = sync

        for sync_type, sync in sync_types.items():
            status_icon = "✅" if sync.success else "❌"
            self.stdout.write(
                f"{status_icon} {sync_type.capitalize()}: {sync.records_processed} records "
                f"({sync.last_sync.strftime('%H:%M:%S')})"
            )
            if not sync.success and sync.error_message:
                self.stdout.write(f"   Error: {sync.error_message}")

        # Show totals
        total_records = sum(sync.records_processed for sync in sync_types.values())
        successful_syncs = sum(1 for sync in sync_types.values() if sync.success)
        total_syncs = len(sync_types)

        self.stdout.write("-" * 50)
        self.stdout.write(f"Total records processed: {total_records}")
        self.stdout.write(f"Successful syncs: {successful_syncs}/{total_syncs}")
        
        if successful_syncs == total_syncs:
            self.stdout.write(self.style.SUCCESS("🎉 All synchronizations completed successfully!"))
        else:
            self.stdout.write(self.style.WARNING("⚠️  Some synchronizations failed. Check logs above."))
