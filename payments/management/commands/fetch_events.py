"""
Management command to fetch only events (programs, offers, videos) from Uscreen API
"""
from django.core.management.base import BaseCommand
from django.core.management import call_command


class Command(BaseCommand):
    help = 'Downloads events (programs, offers, videos) from the Uscreen API.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Number of hours to look back for data (default: 24)'
        )
        parser.add_argument(
            '--full-sync',
            action='store_true',
            help='Perform full synchronization (ignore time range)'
        )

    def handle(self, *args, **options):
        """Fetch programs, offers, and videos"""
        
        self.stdout.write(
            self.style.SUCCESS("Starting events synchronization...")
        )

        # Fetch programs
        self.stdout.write("Fetching programs...")
        call_command('fetch_uscreen_data', type='programs', **options)

        # Fetch offers  
        self.stdout.write("Fetching offers...")
        call_command('fetch_uscreen_data', type='offers', **options)

        # Fetch videos
        self.stdout.write("Fetching videos...")
        call_command('fetch_uscreen_data', type='videos', **options)

        self.stdout.write(
            self.style.SUCCESS("Events synchronization completed successfully!")
        )
