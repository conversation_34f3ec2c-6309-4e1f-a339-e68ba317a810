"""
Test command to verify Uscreen API connectivity and data fetching
"""
import os
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command


class Command(BaseCommand):
    help = 'Test Uscreen API connectivity and fetch a small sample of data.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['programs', 'offers', 'videos', 'users', 'invoices'],
            default='programs',
            help='Type of data to test fetch (default: programs)'
        )

    def handle(self, *args, **options):
        """Test Uscreen API connectivity"""
        
        # Check if API key is set
        api_key = os.environ.get('USCREEN_API_KEY')
        if not api_key:
            self.stdout.write(
                self.style.ERROR("❌ USCREEN_API_KEY environment variable is not set!")
            )
            self.stdout.write("Please set it in your .env file:")
            self.stdout.write("USCREEN_API_KEY=sk_live_your_api_key_here")
            return

        self.stdout.write(
            self.style.SUCCESS(f"✅ USCREEN_API_KEY is set (ends with: ...{api_key[-8:]})")
        )

        sync_type = options['type']
        
        self.stdout.write(
            self.style.SUCCESS(f"🧪 Testing {sync_type} data fetch...")
        )

        try:
            # Test fetch with limited time range (last 24 hours)
            call_command('fetch_uscreen_data', type=sync_type, hours=24)
            
            self.stdout.write(
                self.style.SUCCESS(f"✅ {sync_type.capitalize()} test completed successfully!")
            )
            
            # Show some statistics
            self.show_data_stats(sync_type)
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Test failed: {e}")
            )
            raise

    def show_data_stats(self, sync_type):
        """Show basic statistics about fetched data"""
        from payments.models import (
            UscreenProgram, UscreenOffer, UscreenVideo, 
            UscreenUser, UscreenInvoice, UscreenDataSync
        )
        
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("📊 DATA STATISTICS:")
        self.stdout.write("=" * 50)
        
        # Model mapping
        model_map = {
            'programs': UscreenProgram,
            'offers': UscreenOffer,
            'videos': UscreenVideo,
            'users': UscreenUser,
            'invoices': UscreenInvoice,
        }
        
        if sync_type in model_map:
            model = model_map[sync_type]
            count = model.objects.count()
            self.stdout.write(f"📈 Total {sync_type}: {count}")
            
            if count > 0:
                latest = model.objects.order_by('-created_at').first()
                self.stdout.write(f"🕒 Latest record: {latest.created_at}")
        
        # Show sync log
        latest_sync = UscreenDataSync.objects.filter(sync_type=sync_type).order_by('-last_sync').first()
        if latest_sync:
            status_icon = "✅" if latest_sync.success else "❌"
            self.stdout.write(f"{status_icon} Last sync: {latest_sync.last_sync}")
            self.stdout.write(f"📊 Records processed: {latest_sync.records_processed}")
            if not latest_sync.success and latest_sync.error_message:
                self.stdout.write(f"❌ Error: {latest_sync.error_message}")
        
        self.stdout.write("=" * 50)
