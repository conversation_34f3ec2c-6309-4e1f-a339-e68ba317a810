"""
URL configuration for GoPay-Uscreen integration project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# API Documentation
schema_view = get_schema_view(
    openapi.Info(
        title="GoPay Payments API",
        default_version='v1',
        description="API for GoPay payment integration with Uscreen",
        contact=openapi.Contact(email="<EMAIL>"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

from django.http import JsonResponse

def homepage(request):
    return JsonResponse({
        'message': 'GoPay-Uscreen Payment Integration API',
        'version': '1.0.0',
        'endpoints': {
            'admin': '/admin/',
            'api_docs': '/swagger/',
            'create_payment': '/create-payment/',
            'create_payment_from_product': '/create-payment-from-product/',
            'product_info': '/product/{product_slug}/',
            'webhook': '/notify/',
            'payment_status': '/payment-status/{payment_id}/',
            'test_integration': '/test-integration/'
        },
        'status': 'running'
    })

urlpatterns = [
    path('', homepage, name='homepage'),
    path('admin/', admin.site.urls),

    # API Documentation
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

    # Payments API
    path('api/', include('payments.urls')),
]

# Serve static and media files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
