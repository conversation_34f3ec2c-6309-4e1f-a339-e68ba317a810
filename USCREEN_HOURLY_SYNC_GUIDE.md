# 🕐 Uscreen Ho<PERSON>ová Synchronizace - Návod

## 🎯 **P<PERSON><PERSON>led**

Automatická synchronizace Uscreen dat každou hodinu pro zajištění aktuálních informací o programech, nabídk<PERSON>ch a videích.

---

## ⚙️ **Nastaven<PERSON> Cron Jobs**

### **1. <PERSON><PERSON><PERSON> synchronizace (každou hodinu)**
```bash
0 * * * * cd /opt/gopay-SL24-payments && /opt/gopay-SL24-payments/venv/bin/python manage.py hourly_sync --hours 2
```
- **Frekvence**: Ka<PERSON><PERSON><PERSON> hodin<PERSON> (0. minuta)
- **Data**: Programs, Offers, Videos za poslední 2 hodiny
- **Optimalizace**: Rychlá synchronizace pouze eventů

### **2. Rozšířená synchronizace (každé 4 hodiny)**
```bash
0 */4 * * * cd /opt/gopay-SL24-payments && /opt/gopay-SL24-payments/venv/bin/python manage.py sync_all_uscreen_data --hours 6
```
- **Frekvence**: <PERSON>ždé 4 hodiny
- **Data**: Všechna data (včetně users, invoices) za poslední 6 hodin

### **3. Úplná synchronizace (denně)**
```bash
0 2 * * * cd /opt/gopay-SL24-payments && /opt/gopay-SL24-payments/venv/bin/python manage.py sync_all_uscreen_data --full-sync
```
- **Frekvence**: Denně ve 2:00
- **Data**: Kompletní synchronizace všech dat

---

## 🔧 **Management Commands**

### **1. Hodinová synchronizace**
```bash
# Základní použití
python manage.py hourly_sync

# S podrobným výstupem
python manage.py hourly_sync --verbose

# Vlastní časové okno
python manage.py hourly_sync --hours 3 --verbose
```

### **2. Manuální test synchronizace**
```bash
# Test hodinové synchronizace
python manage.py hourly_sync --verbose

# Test rozšířené synchronizace
python manage.py sync_all_uscreen_data --hours 6

# Test úplné synchronizace
python manage.py sync_all_uscreen_data --full-sync
```

---

## 📊 **Monitoring a Logy**

### **Umístění logů**
```bash
/opt/gopay-SL24-payments/logs/uscreen_sync.log
```

### **Sledování logů v reálném čase**
```bash
tail -f /opt/gopay-SL24-payments/logs/uscreen_sync.log
```

### **Kontrola posledních synchronizací**
```bash
tail -20 /opt/gopay-SL24-payments/logs/uscreen_sync.log
```

### **Kontrola cron jobů**
```bash
crontab -l | grep gopay
```

---

## 📈 **Výkon a Statistiky**

### **Typické časy synchronizace:**
- **Hodinová sync**: ~5-10 sekund (0 nových záznamů)
- **Rozšířená sync**: ~30-60 sekund (závisí na datech)
- **Úplná sync**: ~10-30 minut (všechna data)

### **Očekávané objemy dat:**
- **Programs**: ~85 záznamů (6 stránek)
- **Offers**: ~19 záznamů (3 stránky)
- **Videos**: ~35 záznamů (3 stránky)
- **Users**: ~30 000 záznamů (300+ stránek)
- **Invoices**: ~79 záznamů/týden (4 stránky)

---

## 🔍 **Diagnostika a Řešení Problémů**

### **Kontrola stavu synchronizace**
```python
from payments.models import UscreenDataSync

# Poslední synchronizace
latest_syncs = UscreenDataSync.objects.order_by('-last_sync')[:5]
for sync in latest_syncs:
    print(f"{sync.sync_type}: {sync.success} - {sync.records_processed} records")
```

### **Kontrola aktuálních dat**
```python
from payments.models import UscreenProgram, UscreenOffer, UscreenVideo

print(f"Programs: {UscreenProgram.objects.count()}")
print(f"Offers: {UscreenOffer.objects.count()}")
print(f"Videos: {UscreenVideo.objects.count()}")
```

### **Časté problémy a řešení**

#### **1. Cron job neběží**
```bash
# Kontrola cron služby
sudo systemctl status cron

# Restart cron služby
sudo systemctl restart cron
```

#### **2. Chyby v logu**
```bash
# Kontrola chyb
grep -i error /opt/gopay-SL24-payments/logs/uscreen_sync.log

# Kontrola posledních chyb
tail -50 /opt/gopay-SL24-payments/logs/uscreen_sync.log | grep -i error
```

#### **3. API klíč problémy**
```bash
# Kontrola API klíče
cd /opt/gopay-SL24-payments
python manage.py test_uscreen_fetch --type programs
```

---

## 🚀 **Výhody Hodinové Synchronizace**

✅ **Aktuální data** - Nové programy a nabídky do 1 hodiny  
✅ **Rychlá odezva** - Pouze změněná data za poslední 2 hodiny  
✅ **Automatické slugy** - Generování URL-friendly slugů z titulů  
✅ **Robustní pagination** - Zpracování všech stránek automaticky  
✅ **Rate limiting** - Respektování API limitů  
✅ **Monitoring** - Detailní logy a statistiky  
✅ **Fallback** - Rozšířená a úplná synchronizace jako záloha  

---

## 📋 **Shrnutí Nastavení**

**Hodinová synchronizace je nyní aktivní a automaticky stahuje data za poslední 2 hodiny každou hodinu!**

- ⏰ **Každou hodinu**: Events (programs, offers, videos)
- ⏰ **Každé 4 hodiny**: Všechna data za 6 hodin
- ⏰ **Denně**: Kompletní synchronizace
- 📊 **Logy**: `/opt/gopay-SL24-payments/logs/uscreen_sync.log`
